<nav class="navbar navbar-expand-lg bg-body-tertiary shadow-sm">
    <div class="container">
        <a class="navbar-brand fw-bold" href="/dashboard">
            <img src="/static/mom.ico" alt="MDM" width="32" height="32"
                 class="d-inline-block align-text-top me-2 rounded-circle nav-logo">
            KouriChat
        </a>

        {% if session.get('logged_in') %}
        <!-- 登录后显示的内容 -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarContent" aria-controls="navbarContent" aria-expanded="false" aria-label="切换导航菜单">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarContent">
            <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if active_page == 'dashboard' }}" href="/dashboard">
                        <i class="bi bi-house-door me-1"></i>主页
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if active_page == 'config' }}" href="/config">
                        <i class="bi bi-gear me-1"></i>配置中心
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if active_page == 'edit_avatar' }}" href="/edit_avatar">
                        <i class="bi bi-person-circle me-1"></i>角色设定
                    </a>
                </li>
            </ul>

            <div class="d-flex align-items-center ms-auto">
                <div class="form-check form-switch me-3">
                    <input class="form-check-input" type="checkbox" role="switch"
                           id="darkModeToggle" data-dark-toggle>
                    <label class="form-check-label" for="darkModeToggle">
                        <i class="bi bi-moon-stars me-1"></i>护眼模式
                    </label>
                </div>
                <div>
                    <label for="backgroundInput" class="d-none">选择背景图片</label>
                    <input type="file" class="d-none" id="backgroundInput" accept="image/*">
                    <button class="btn btn-outline-primary" type="button" onclick="handleBackgroundClick()">
                        <i class="bi bi-image me-2"></i>更换背景
                    </button>
                </div>
            </div>
        </div>
        {% else %}
        <!-- 未登录时显示的内容 -->
        <div class="d-flex align-items-center">
            <div class="form-check form-switch me-3">
                <input class="form-check-input" type="checkbox" role="switch"
                       id="darkModeToggle" data-dark-toggle>
                <label class="form-check-label" for="darkModeToggle">
                    <i class="bi bi-moon-stars me-1"></i>护眼模式
                </label>
            </div>
        </div>
        {% endif %}
    </div>
</nav>

<script>
function handleBackgroundClick() {
    const input = document.getElementById('backgroundInput');
    input.click();
}

document.getElementById('backgroundInput').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const formData = new FormData();
        formData.append('background', file);

        // 显示加载状态
        const button = this.nextElementSibling;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="bi bi-arrow-repeat me-2 spin"></i>上传中...';
        button.disabled = true;

        fetch('/upload_background', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                document.body.style.backgroundImage = `url('${data.path}')`;
                showToast('背景更新成功', 'success');
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            showToast('上传失败：' + error, 'error');
        })
        .finally(() => {
            // 恢复按钮状态
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
});

function showManualAnnouncement() {
    // 尝试获取公告模态框实例并显示
    const announcementModalElement = document.getElementById('announcementModal');
    if (announcementModalElement) {
        const announcementModal = bootstrap.Modal.getOrCreateInstance(announcementModalElement);

        // 如果公告内容尚未加载，则先加载
        const contentElement = document.getElementById('announcementContent');
        if (!contentElement.innerHTML.trim()) {
            fetch('/get_announcement')
                .then(response => response.json())
                .then(data => {
                    if (data.enabled) {
                        // 设置公告内容
                        const modalTitleElement = document.getElementById('announcementModalLabel');
                        modalTitleElement.textContent = data.title;
                        modalTitleElement.classList.remove('fs-4');
                        modalTitleElement.classList.add('fw-bold', 'text-center', 'w-100', 'fs-3');
                        contentElement.innerHTML = data.content;

                        const modalHeader = document.querySelector('#announcementModal .modal-header');
                        modalHeader.classList.remove('bg-info', 'bg-warning', 'bg-danger', 'bg-success', 'text-white', 'text-dark');
                        modalHeader.style.backgroundColor = '';

                        // 绑定"不再显示"按钮事件
                        bindAnnouncementDismissHandler(data);
                        
                        // 显示模态框
                        announcementModal.show();
                    } else {
                        showToast('公告当前已禁用', 'warning');
                    }
                })
                .catch(error => {
                    console.error("获取公告失败:", error);
                    showToast('获取公告失败', 'error');
                });
        } else {
            // 如果内容已加载，直接显示
            announcementModal.show();
        }
    } else {
        console.error("找不到公告模态框元素");
        showToast('公告功能异常', 'error');
    }
}
</script>

<style>
/* 添加logo样式 */
.nav-logo {
    object-fit: cover;
}

/* 在现有的 style 标签中添加 */
.nav-item {
    position: relative;
    transition: all 0.3s ease;
}

.nav-item::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-item:hover::after {
    width: 100%;
}

.nav-link {
    transition: all 0.3s ease;
}

.nav-link:hover {
    transform: translateY(-2px);
}

/* 优化下拉菜单动画 */
.navbar-collapse {
    transition: all 0.3s ease;
}

/* 优化移动端菜单按钮 */
.navbar-toggler {
    transition: all 0.3s ease;
}

.navbar-toggler:hover {
    transform: rotate(90deg);
}

/* 修复护眼模式切换按钮样式 */
.navbar .form-check.form-switch {
    display: flex;
    align-items: center;
    height: 100%;
    margin: 0;
}

.navbar .form-check-label {
    display: flex;
    align-items: center;
    margin: 0 0 0 8px;
    line-height: 1;
}

.navbar .form-check-input {
    margin: 0;
    vertical-align: middle;
}

/* 添加统一的选中状态样式 */
.navbar .form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.spin {
    animation: spin 1s linear infinite;
}
</style>