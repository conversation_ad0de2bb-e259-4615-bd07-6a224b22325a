{"version": "*******", "last_update": "2025-08-09", "description": "新增热更新模块；新增语音通话提示功能；新增世界书功能；意图识别模块支持单独设置更加低成本的小模型；为保证用户体验，在模型效果产生波动时会依照默认顺序使用其他模型生成回答；修复历史遗留问题一处，降低了报错500的概率", "download_url": "https://git.kourichat.com/KouriChat-Main/cloud-delivery-repo/releases/download/v{version}/kourichat-v{version}.zip", "file_size": "约 15.2 MB", "checksum": "sha256:abcd1234...", "changelog": ["优化了网络连接稳定性", "提升了API响应速度", "修复了文本处理相关问题", "增强了系统安全性"], "is_critical": false, "min_version": "1.0.0", "update_type": "optional"}