# 定时发送消息模块复刻AI提示词

## 背景
您需要复刻一个完整的定时发送消息模块，该模块来自一个微信聊天机器人项目。该模块包含三个核心子系统：AutoTasker（主定时任务系统）、AutoSendHandler（自动发送处理器）和ReminderService（提醒服务）。这些模块协同工作，实现灵活的定时消息发送功能。

## 目的
请严格按照以下技术规格实现一个功能完整的定时发送消息模块，确保模块间接口兼容性和功能完整性。所有代码必须遵循Python最佳实践，包含完整的异常处理和日志记录。

## 技术架构要求

### 依赖库清单
```
apscheduler==3.10.4
python-dateutil==2.9.0.post0
threading (内置)
json (内置)
datetime (内置)
logging (内置)
```

## 模块一：AutoTasker（主定时任务系统）

### 核心功能
- 支持cron表达式和间隔时间两种调度方式
- 任务配置持久化到JSON文件
- 动态添加、删除、切换任务状态
- 线程安全的任务管理

### 类结构实现
```python
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from datetime import datetime
import logging
import json
import os

class AutoTasker:
    def __init__(self, message_handler, task_file_path="data/tasks.json"):
        """
        初始化自动任务管理器

        Args:
            message_handler: 消息处理器实例，用于发送消息
            task_file_path: 任务配置文件路径
        """
        # 初始化调度器、消息处理器、任务字典
        # 确保任务文件目录存在
        # 加载已存在的任务配置
        # 启动后台调度器

    def add_task(self, task_id, chat_id, content, schedule_type, schedule_time, interval=None, is_active=True):
        """
        添加新任务

        Args:
            task_id: 任务ID
            chat_id: 接收消息的聊天ID
            content: 消息内容
            schedule_type: 调度类型 ('cron' 或 'interval')
            schedule_time: 调度时间 (cron表达式 或 具体时间)
            interval: 间隔时间（秒），仅用于 interval 类型
            is_active: 是否激活任务
        """
        # 创建 cron 或 interval 触发器
        # 定义任务执行函数（调用 message_handler.add_to_queue）
        # 添加到调度器并保存任务信息
        # 保存配置文件

    def remove_task(self, task_id):
        """删除任务"""
        # 从调度器移除任务
        # 从任务字典删除
        # 保存配置文件

    def toggle_task(self, task_id):
        """切换任务激活状态"""
        # 切换任务的is_active状态
        # 保存配置文件

    def load_tasks(self):
        """从配置文件加载任务列表"""
        # JSON 文件读取操作
        # 重新创建所有任务

    def save_tasks(self):
        """保存任务配置到文件"""
        # JSON 文件写入操作

    def get_task(self, task_id):
        """获取单个任务信息"""

    def get_all_tasks(self):
        """获取所有任务信息"""

    def __del__(self):
        """清理资源"""
        # 关闭调度器
```

### 任务配置文件格式
```json
[
    {
        "task_id": "unique_task_id",
        "chat_id": "target_chat_id",
        "content": "消息内容",
        "schedule_type": "cron|interval",
        "schedule_time": "cron表达式或秒数",
        "interval": "间隔秒数（仅interval类型）",
        "is_active": true
    }
]
```

### 任务执行函数模板
```python
def task_func():
    try:
        if self.tasks[task_id]['is_active']:
            task_chat_id = self.tasks[task_id]['chat_id']
            self.message_handler.add_to_queue(
                chat_id=task_chat_id,
                content=content,
                sender_name="System",
                username="AutoTasker",
                is_group=False
            )
            logger.info(f"执行定时任务 {task_id} 发送给 {task_chat_id}")
    except Exception as e:
        logger.error(f"执行任务 {task_id} 失败: {str(e)}")
```

## 模块二：AutoSendHandler（自动发送处理器）

### 核心功能
- 随机时间间隔的自动消息发送
- 安静时间段检查机制
- 从监听列表中随机选择发送对象
- 基于Timer的倒计时实现

### 类结构实现
```python
import threading
import random
from datetime import datetime, time
import logging

class AutoSendHandler:
    def __init__(self, message_handler, config):
        """
        初始化自动发送处理器

        Args:
            message_handler: 消息处理器实例
            config: 配置对象，包含auto_message和quiet_time设置
        """
        # 初始化配置、定时器、监听列表
        # 设置未回复计数器

    def start(self):
        """开始自动发送循环"""
        # 启动第一次倒计时

    def auto_send_message(self):
        """自动发送消息"""
        # 检查安静时间
        # 随机选择用户
        # 发送消息到队列
        # 启动下一次倒计时

    def is_quiet_time(self):
        """检查当前时间是否在安静时间段内"""
        # 解析安静时间配置
        # 处理跨日期的时间段
        # 返回布尔值

    def get_random_countdown_time(self):
        """在最小和最大小时数之间随机选择"""
        # 使用random.uniform生成随机小时数
        # 转换为秒数返回

    def start_countdown(self):
        """创建新的Timer定时器"""
        # 取消现有定时器
        # 计算随机倒计时时间
        # 创建新的threading.Timer
        # 设置daemon=True并启动

    def stop(self):
        """停止自动发送消息"""
        # 取消定时器
        # 重置状态标志
```

### 配置数据结构
```python
from dataclasses import dataclass

@dataclass
class AutoMessageSettings:
    content: str  # 自动消息内容
    min_hours: float  # 最小间隔小时
    max_hours: float  # 最大间隔小时

@dataclass
class QuietTimeSettings:
    start: str  # 安静时间开始 "22:00"
    end: str    # 安静时间结束 "08:00"
```

## 模块三：ReminderService（提醒服务）

### 核心功能
- 后台线程轮询检查提醒时间
- 支持文本和语音两种提醒方式
- 线程安全的任务管理
- 动态添加和取消提醒任务

### 类结构实现
```python
import threading
import time
from datetime import datetime
from typing import Dict, List
import logging

class ReminderTask:
    """单个提醒任务数据结构"""
    def __init__(self, task_id: str, chat_id: str, target_time: datetime,
                 content: str, sender_name: str, reminder_type: str = "text"):
        self.task_id = task_id
        self.chat_id = chat_id
        self.target_time = target_time
        self.content = content
        self.sender_name = sender_name
        self.reminder_type = reminder_type
        self.audio_path = None

    def is_due(self) -> bool:
        """检查是否到达提醒时间"""
        return datetime.now() >= self.target_time

class ReminderService:
    def __init__(self, message_handler, mem_service):
        """
        初始化提醒服务

        Args:
            message_handler: 消息处理器实例
            mem_service: 内存服务实例
        """
        # 初始化消息处理器、内存服务
        # 创建活跃提醒字典和线程锁
        # 启动后台轮询线程

    def add_reminder(self, chat_id: str, target_time: datetime, content: str,
                    sender_name: str, reminder_type: str = "text"):
        """添加提醒任务"""
        # 生成唯一task_id
        # 创建ReminderTask实例
        # 如果是语音类型，预生成音频文件
        # 线程安全地添加到活跃提醒字典

    def cancel_reminder(self, task_id: str) -> bool:
        """取消提醒任务"""
        # 线程安全地从字典中删除任务
        # 返回操作结果

    def list_reminders(self) -> List[Dict]:
        """获取所有活跃提醒任务列表"""
        # 线程安全地返回任务信息列表

    def _start_polling_thread(self):
        """启动后台轮询线程"""
        # 创建daemon线程
        # 启动_poll_reminders_loop

    def _poll_reminders_loop(self):
        """无限循环检查到期任务"""
        # while True循环
        # 每秒检查一次到期任务
        # 执行到期的提醒任务
        # 从活跃字典中移除已执行任务

    def _do_remind(self, task: ReminderTask, wx):
        """根据提醒类型发送文本或语音消息"""
        # 生成提醒消息内容
        # 根据reminder_type选择发送方式
        # 文本：调用message_handler.handle_user_message
        # 语音：调用语音通话功能

    def _get_reminder_prompt(self, content: str) -> str:
        """生成提醒消息的提示词"""
        # 返回格式化的提醒提示词
```

## 消息队列集成规范

### 统一消息发送接口
所有模块都必须通过以下统一接口发送消息：

```python
message_handler.add_to_queue(
    chat_id=target_chat_id,
    content=message_content,
    sender_name="System",
    username="AutoTasker|AutoSend|Reminder",
    is_group=False
)
```

### 消息队列参数说明
- `chat_id`: 目标聊天对象ID
- `content`: 消息内容
- `sender_name`: 发送者名称（系统消息使用"System"）
- `username`: 模块标识（用于日志追踪）
- `is_group`: 是否为群聊消息（定时消息通常为False）

## 关键技术实现要点

### 1. 线程安全
- 使用`threading.Lock()`保护共享资源
- 所有字典操作都要在锁保护下进行
- 定时器操作要考虑并发安全

### 2. 异常处理
- 每个关键操作都要有try-catch块
- 记录详细的错误日志
- 优雅降级处理

### 3. 日志记录
```python
import logging
logger = logging.getLogger(__name__)

# 使用示例
logger.info(f"执行定时任务 {task_id}")
logger.error(f"任务执行失败: {str(e)}")
logger.debug(f"任务状态更新: {task_status}")
```

### 4. 配置持久化
- JSON文件格式存储
- 确保文件目录存在
- 原子性写入操作
- 备份机制考虑

### 5. 时间处理
```python
from datetime import datetime, timedelta, time
import dateutil.parser

# 时间解析示例
target_time = datetime.strptime("2024-01-01 10:30:00", "%Y-%m-%d %H:%M:%S")
current_time = datetime.now()
time_diff = target_time - current_time
```

### 6. 优雅关闭
- 实现`__del__`方法
- 正确关闭调度器和线程
- 保存未完成的任务状态

## 输出要求

### 代码质量标准
1. 遵循PEP 8编码规范
2. 包含完整的类型注解
3. 详细的docstring文档
4. 完整的异常处理
5. 充分的日志记录

### 文件结构
```
src/
├── AutoTasker/
│   └── autoTasker.py
├── handlers/
│   ├── autosend.py
│   └── message.py
└── modules/
    └── reminder/
        └── service.py
```

### 测试验证
实现完成后，请提供：
1. 基本功能测试用例
2. 异常情况处理验证
3. 并发安全性测试
4. 配置文件读写测试

## 风格要求
- 代码注释使用中文
- 变量命名使用英文，遵循snake_case
- 类名使用PascalCase
- 常量使用UPPER_CASE

## 受众
此模块将被有经验的Python开发者使用和维护，需要确保代码的可读性、可维护性和扩展性。

请严格按照以上规格实现，确保每个模块都能独立工作，同时保持良好的模块间协作能力。