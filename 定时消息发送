# 定时发送消息模块复刻AI提示词

## 背景
您需要复刻一个完整的定时发送消息模块，该模块来自一个微信聊天机器人项目。该模块包含三个核心子系统：AutoTasker（主定时任务系统）、AutoSendHandler（自动发送处理器）和ReminderService（提醒服务）。这些模块协同工作，实现灵活的定时消息发送功能。

## 目的
请严格按照以下完整代码实现一个功能完整的定时发送消息模块，确保模块间接口兼容性和功能完整性。所有代码必须遵循Python最佳实践，包含完整的异常处理和日志记录。

## 技术架构要求

### 依赖库清单
```
apscheduler==3.10.4
python-dateutil==2.9.0.post0
threading (内置)
json (内置)
datetime (内置)
logging (内置)
```

## 模块一：AutoTasker（主定时任务系统）

### 完整代码实现
```python
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from datetime import datetime
import logging
import json
import os

logger = logging.getLogger(__name__)

class AutoTasker:
    def __init__(self, message_handler, task_file_path="data/tasks.json"):
        """
        初始化自动任务管理器

        Args:
            message_handler: 消息处理器实例，用于发送消息
            task_file_path: 任务配置文件路径
        """
        self.message_handler = message_handler
        self.task_file_path = task_file_path
        self.scheduler = BackgroundScheduler()
        self.tasks = {}

        # 确保任务文件目录存在
        os.makedirs(os.path.dirname(task_file_path), exist_ok=True)

        # 加载已存在的任务
        self.load_tasks()

        # 启动调度器
        self.scheduler.start()
        logger.info("AutoTasker 初始化完成")

    def load_tasks(self):
        """从配置文件加载任务列表"""
        try:
            if os.path.exists(self.task_file_path):
                with open(self.task_file_path, 'r', encoding='utf-8') as f:
                    tasks_list = json.load(f)

                # 确保tasks_list是列表
                if not isinstance(tasks_list, list):
                    tasks_list = []

                # 清空现有任务
                for task_id in list(self.tasks.keys()):
                    self.remove_task(task_id)

                # 加载每个任务
                for task in tasks_list:
                    if isinstance(task, dict) and 'task_id' in task:
                        self.add_task(
                            task_id=task['task_id'],
                            chat_id=task['chat_id'],
                            content=task['content'],
                            schedule_type=task['schedule_type'],
                            schedule_time=task['schedule_time'],
                            interval=task.get('interval'),
                            is_active=task.get('is_active', True)
                        )
                logger.info(f"成功加载 {len(tasks_list)} 个任务")
            else:
                logger.info("任务配置文件不存在，将创建新文件")
        except Exception as e:
            logger.info(f"加载任务失败: {str(e)}")
            # 确保tasks字典为空
            self.tasks = {}

    def save_tasks(self):
        """保存任务配置到文件"""
        try:
            # 将任务转换为列表格式
            tasks_list = []
            for task_id, task in self.tasks.items():
                task_data = {
                    'task_id': task_id,
                    'chat_id': task['chat_id'],
                    'content': task['content'],
                    'schedule_type': task['schedule_type'],
                    'schedule_time': task['schedule_time'],
                    'interval': task.get('interval'),
                    'is_active': task['is_active']
                }
                tasks_list.append(task_data)

            with open(self.task_file_path, 'w', encoding='utf-8') as f:
                json.dump(tasks_list, f, ensure_ascii=False, indent=4)
            logger.info(f"任务配置已保存，共 {len(tasks_list)} 个任务")
        except Exception as e:
            logger.error(f"保存任务失败: {str(e)}")

    def add_task(self, task_id, chat_id, content, schedule_type, schedule_time, interval=None, is_active=True):
        """
        添加新任务

        Args:
            task_id: 任务ID
            chat_id: 接收消息的聊天ID
            content: 消息内容
            schedule_type: 调度类型 ('cron' 或 'interval')
            schedule_time: 调度时间 (cron表达式 或 具体时间)
            interval: 间隔时间（秒），仅用于 interval 类型
            is_active: 是否激活任务
        """
        try:
            if schedule_type == 'cron':
                trigger = CronTrigger.from_crontab(schedule_time)
            elif schedule_type == 'interval':
                # 确保interval是有效的整数
                if not schedule_time or not str(schedule_time).isdigit():
                    raise ValueError(f"无效的时间间隔: {schedule_time}")
                trigger = IntervalTrigger(seconds=int(schedule_time))
            else:
                raise ValueError(f"不支持的调度类型: {schedule_type}")

            # 创建任务执行函数
            def task_func():
                try:
                    if self.tasks[task_id]['is_active']:
                        # 使用任务中保存的chat_id
                        task_chat_id = self.tasks[task_id]['chat_id']
                        self.message_handler.add_to_queue(
                            chat_id=task_chat_id,
                            content=content,
                            sender_name="System",
                            username="AutoTasker",
                            is_group=False
                        )
                        logger.info(f"执行定时任务 {task_id} 发送给 {task_chat_id}")
                except Exception as e:
                    logger.error(f"执行任务 {task_id} 失败: {str(e)}")

            # 添加任务到调度器
            job = self.scheduler.add_job(
                task_func,
                trigger=trigger,
                id=task_id
            )

            # 保存任务信息
            self.tasks[task_id] = {
                'chat_id': chat_id,
                'content': content,
                'schedule_type': schedule_type,
                'schedule_time': schedule_time,
                'interval': schedule_time if schedule_type == 'interval' else None,
                'is_active': is_active,
                'job': job
            }

            self.save_tasks()
            logger.info(f"添加任务成功: {task_id}")

        except Exception as e:
            logger.error(f"添加任务失败: {str(e)}")
            raise

    def remove_task(self, task_id):
        """删除任务"""
        try:
            if task_id in self.tasks:
                self.tasks[task_id]['job'].remove()
                del self.tasks[task_id]
                self.save_tasks()
                logger.info(f"删除任务成功: {task_id}")
            else:
                logger.warning(f"任务不存在: {task_id}")
        except Exception as e:
            logger.error(f"删除任务失败: {str(e)}")

    def update_task(self, task_id, **kwargs):
        """更新任务配置"""
        try:
            if task_id not in self.tasks:
                raise ValueError(f"任务不存在: {task_id}")

            task = self.tasks[task_id]

            # 更新任务参数
            for key, value in kwargs.items():
                if key in task:
                    task[key] = value

            # 如果需要更新调度
            if 'schedule_type' in kwargs or 'schedule_time' in kwargs or 'interval' in kwargs:
                self.remove_task(task_id)
                self.add_task(
                    task_id=task_id,
                    chat_id=task['chat_id'],
                    content=task['content'],
                    schedule_type=task['schedule_type'],
                    schedule_time=task['schedule_time'],
                    interval=task.get('interval'),
                    is_active=task['is_active']
                )
            else:
                self.save_tasks()

            logger.info(f"更新任务成功: {task_id}")

        except Exception as e:
            logger.error(f"更新任务失败: {str(e)}")
            raise

    def toggle_task(self, task_id):
        """切换任务的激活状态"""
        try:
            if task_id in self.tasks:
                self.tasks[task_id]['is_active'] = not self.tasks[task_id]['is_active']
                self.save_tasks()
                status = "激活" if self.tasks[task_id]['is_active'] else "暂停"
                logger.info(f"任务 {task_id} 已{status}")
            else:
                logger.warning(f"任务不存在: {task_id}")
        except Exception as e:
            logger.error(f"切换任务状态失败: {str(e)}")

    def get_task(self, task_id):
        """获取任务信息"""
        return self.tasks.get(task_id)

    def get_all_tasks(self):
        """获取所有任务信息"""
        return {
            task_id: {
                k: v for k, v in task_info.items() if k != 'job'
            }
            for task_id, task_info in self.tasks.items()
        }

    def __del__(self):
        """清理资源"""
        if hasattr(self, 'scheduler'):
            self.scheduler.shutdown()
```

## 模块二：AutoSendHandler（自动发送处理器）

### 完整代码实现
```python
"""
自动发送消息处理模块
负责处理自动发送消息的逻辑，包括:
- 倒计时管理
- 消息发送
- 安静时间控制
"""

import logging
import random
import threading
from datetime import datetime, timedelta

logger = logging.getLogger('main')

class AutoSendHandler:
    def __init__(self, message_handler, config, listen_list):
        self.message_handler = message_handler
        self.config = config
        self.listen_list = listen_list

        # 计时器相关
        self.countdown_timer = None
        self.is_countdown_running = False
        self.countdown_end_time = None
        self.unanswered_count = 0
        self.last_chat_time = None

    def update_last_chat_time(self):
        """更新最后一次聊天时间"""
        self.last_chat_time = datetime.now()
        self.unanswered_count = 0
        logger.info(f"更新最后聊天时间: {self.last_chat_time}，重置未回复计数器为0")

    def is_quiet_time(self) -> bool:
        """检查当前是否在安静时间段内"""
        try:
            current_time = datetime.now().time()
            quiet_start = datetime.strptime(self.config.behavior.quiet_time.start, "%H:%M").time()
            quiet_end = datetime.strptime(self.config.behavior.quiet_time.end, "%H:%M").time()

            if quiet_start <= quiet_end:
                # 如果安静时间不跨天
                return quiet_start <= current_time <= quiet_end
            else:
                # 如果安静时间跨天（比如22:00到次日08:00）
                return current_time >= quiet_start or current_time <= quiet_end
        except Exception as e:
            logger.error(f"检查安静时间出错: {str(e)}")
            return False

    def get_random_countdown_time(self):
        """获取随机倒计时时间"""
        min_seconds = int(self.config.behavior.auto_message.min_hours * 3600)
        max_seconds = int(self.config.behavior.auto_message.max_hours * 3600)
        return random.uniform(min_seconds, max_seconds)

    def auto_send_message(self):
        """自动发送消息"""
        if self.is_quiet_time():
            logger.info("当前处于安静时间，跳过自动发送消息")
            self.start_countdown()
            return

        if self.listen_list:
            user_id = random.choice(self.listen_list)
            self.unanswered_count += 1
            reply_content = f"{self.config.behavior.auto_message.content} 这是对方可能因为在忙碌或者有自己的事没有回复你，根据上下文联系，判断用户现在的状态，回复符合角色的话语。"
            logger.info(f"自动发送消息到 {user_id}: {reply_content}")
            try:
                self.message_handler.add_to_queue(
                    chat_id=user_id,
                    content=reply_content,
                    sender_name="System",
                    username="System",
                    is_group=False
                )
                self.start_countdown()
            except Exception as e:
                logger.error(f"自动发送消息失败: {str(e)}")
                self.start_countdown()
        else:
            logger.error("没有可用的聊天对象")
            self.start_countdown()

    def start_countdown(self):
        """开始新的倒计时"""
        if self.countdown_timer:
            self.countdown_timer.cancel()

        countdown_seconds = self.get_random_countdown_time()
        self.countdown_end_time = datetime.now() + timedelta(seconds=countdown_seconds)
        logger.info(f"开始新的倒计时: {countdown_seconds/3600:.2f}小时")

        self.countdown_timer = threading.Timer(countdown_seconds, self.auto_send_message)
        self.countdown_timer.daemon = True
        self.countdown_timer.start()
        self.is_countdown_running = True

    def stop(self):
        """停止自动发送消息"""
        if self.countdown_timer:
            self.countdown_timer.cancel()
            self.countdown_timer = None
        self.is_countdown_running = False
        logger.info("自动发送消息已停止")
```

## 模块三：ReminderService（提醒服务）

### 完整代码实现
```python
import logging
import threading
import time
import os
import sys
from datetime import datetime
from typing import Dict, List
from wxauto import WeChat

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../")))
from modules.reminder.call import Call
from modules.tts.service import tts
from modules.memory import MemoryService
from src.handlers.message import MessageHandler
from src.services.ai.llm_service import LLMService
from data.config import config

logger = logging.getLogger('main')


class ReminderTask:
    """单个提醒任务结构"""
    def __init__(self, task_id: str, chat_id: str, target_time: datetime,
                 content: str, sender_name: str, reminder_type: str = "text"):
        self.task_id = task_id
        self.chat_id = chat_id
        self.target_time = target_time
        self.content = content
        self.sender_name = sender_name
        self.reminder_type = reminder_type
        self.audio_path = None

    def is_due(self) -> bool:
        return datetime.now() >= self.target_time


class ReminderService:
    def __init__(self, message_handler: MessageHandler, mem_service: MemoryService):
        self.message_handler = message_handler
        self.wx = message_handler.wx
        self.mem_service = mem_service
        self.llm_service = message_handler.deepseek
        self.active_reminders: Dict[str, ReminderTask] = {}
        self._lock = threading.Lock()
        self._start_polling_thread()
        logger.info("统一提醒服务已启动")

    def _start_polling_thread(self):
        thread = threading.Thread(target=self._poll_reminders_loop, daemon=True)
        thread.start()

    def _poll_reminders_loop(self):
        while True:
            due_tasks: List[ReminderTask] = []
            with self._lock:
                for _, task in list(self.active_reminders.items()):
                    if task.is_due():
                        due_tasks.append(task)
                for task in due_tasks:
                    del self.active_reminders[task.task_id]
            for task in due_tasks:
                logger.info(f"到达提醒时间，执行提醒: {task.task_id}")
                self._do_remind(task, self.wx)
            time.sleep(1)

    def _do_remind(self, task: ReminderTask, wx: WeChat):
        try:
            prompt = self._get_reminder_prompt(task.content)
            logger.debug(f"生成提醒消息 - 用户: {task.sender_name}, 类型: {task.reminder_type}, 提示词: {prompt}")

            if task.reminder_type == "voice":
                Call(wx=wx, who=task.sender_name, audio_file_path=task.audio_path)
                tts._del_audio_file(task.audio_path)
            else:
                self.message_handler.handle_user_message(
                    content=prompt,
                    chat_id=task.chat_id,
                    sender_name="System",
                    username="System",
                    is_group=False
                )
            logger.info(f"已发送提醒消息给 {task.sender_name}")
        except Exception as e:
            logger.error(f"发送提醒消息失败: {str(e)}")

    def _remind_text_generate(self, remind_content: str, sender_name: str):
        core_mem = self.mem_service.get_core_memory(avatar_name=self.message_handler.current_avatar, user_id=sender_name)
        context = self.mem_service.get_recent_context(avatar_name=self.message_handler.current_avatar, user_id=sender_name)
        sys_prompt = f"你将进行角色扮演，请你同用户进行符合人设的交流沟通。你的人设如下：\n\n{self.message_handler.prompt_content}\n\n"
        sys_prompt = sys_prompt + f"另外，作为一个仿真的角色扮演者，你需要掌握一些你不一定用到的、但是十分重要的知识：{core_mem}。你的每次回应都不应该违反这些知识！"
        messages = [{"role": "system", "content": sys_prompt}, *context[-self.message_handler.max_groups * 2:]]
        sys_prompt = f"现在提醒时间到了，用户之前设定的提示内容为"{remind_content}"。请以你的人设中的身份主动找用户聊天。保持角色设定的一致性和上下文的连贯性。"
        messages.append({"role": "system", "content": sys_prompt})
        request_config = {
                        "model": self.message_handler.model,
                        "messages": messages,
                        "temperature": self.message_handler.temperature,
                        "max_tokens": self.message_handler.max_token,
                    }
        response = self.llm_service.client.chat.completions.create(**request_config)
        raw_content = response.choices[0].message.content
        return raw_content


    def add_reminder(self, chat_id: str, target_time: datetime, content: str, sender_name: str, reminder_type: str = "text"):
        try:
            task_id = f"reminder_{chat_id}_{datetime.now().timestamp()}"
            task = ReminderTask(task_id, chat_id, target_time, content, sender_name, reminder_type)
            if reminder_type == "voice":
                logger.info("检测到语音提醒任务，预生成回复中")
                remind_text = self._remind_text_generate(remind_content=content, sender_name=sender_name)
                logger.info(f"预生成回复:{tts._clear_tts_text(remind_text)}")
                logger.info("生成语音中")
                audio_file_path = tts._generate_audio_file(tts._clear_tts_text(remind_text))
                # 语音生成失败，退化为文本提醒
                if audio_file_path is None:
                    logger.warning("提醒任务语音生成失败，将替换为文本提醒任务")
                    fixed_task = ReminderTask(task_id, chat_id, target_time, content, sender_name, reminder_type="text")
                    with self._lock:
                        self.active_reminders[task_id] = fixed_task
                    logger.info(f"提醒任务已添加。提醒时间: {target_time}, 内容: {content}，用户：{sender_name}，类型：{reminder_type}")
                # 语音生成成功，保存音频路径到 task 属性中
                else:
                    task.audio_path = audio_file_path
                    logger.info("提醒任务语音生成完成")
                    with self._lock:
                        self.active_reminders[task_id] = task
                    logger.info(f"提醒任务已添加。提醒时间: {target_time}, 内容: {content}，用户：{sender_name}，类型：{reminder_type}")
            else:
                with self._lock:
                    self.active_reminders[task_id] = task
                logger.info(f"提醒任务已添加。提醒时间: {target_time}, 内容: {content}，用户：{sender_name}，类型：{reminder_type}")
        except Exception as e:
            logger.error(f"添加提醒任务失败: {str(e)}")

    def cancel_reminder(self, task_id: str) -> bool:
        with self._lock:
            if task_id in self.active_reminders:
                del self.active_reminders[task_id]
                logger.info(f"提醒任务已取消: {task_id}")
                return True
            return False

    def list_reminders(self) -> List[Dict]:
        with self._lock:
            return [{
                'task_id': task_id,
                'chat_id': task.chat_id,
                'target_time': task.target_time.isoformat(),
                'content': task.content,
                'sender_name': task.sender_name,
                'reminder_type': task.reminder_type
            } for task_id, task in self.active_reminders.items()]

    def _get_reminder_prompt(self, content: str) -> str:
        return f"""现在提醒时间到了，用户之前设定的提示内容为"{content}"。请以你的人设中的身份主动找用户聊天。保持角色设定的一致性和上下文的连贯性"""


'''
单独对模块进行调试时，可以使用该代码
'''
if __name__ == '__main__':
    pass
```

## 消息队列集成规范

### 统一消息发送接口
所有模块都必须通过以下统一接口发送消息：

```python
message_handler.add_to_queue(
    chat_id=target_chat_id,
    content=message_content,
    sender_name="System",
    username="AutoTasker|AutoSend|Reminder",
    is_group=False
)
```

### 消息队列参数说明
- `chat_id`: 目标聊天对象ID
- `content`: 消息内容
- `sender_name`: 发送者名称（系统消息使用"System"）
- `username`: 模块标识（用于日志追踪）
- `is_group`: 是否为群聊消息（定时消息通常为False）

## 任务配置文件格式

### JSON配置示例
```json
[
    {
        "task_id": "daily_greeting",
        "chat_id": "user123",
        "content": "早上好！新的一天开始了",
        "schedule_type": "cron",
        "schedule_time": "0 8 * * *",
        "interval": null,
        "is_active": true
    },
    {
        "task_id": "hourly_check",
        "chat_id": "user456",
        "content": "定时检查消息",
        "schedule_type": "interval",
        "schedule_time": "3600",
        "interval": "3600",
        "is_active": true
    }
]
```

## 关键技术实现要点

### 1. 线程安全
- 使用`threading.Lock()`保护共享资源
- 所有字典操作都要在锁保护下进行
- 定时器操作要考虑并发安全

### 2. 异常处理
- 每个关键操作都要有try-catch块
- 记录详细的错误日志
- 优雅降级处理

### 3. 日志记录
```python
import logging
logger = logging.getLogger(__name__)

# 使用示例
logger.info(f"执行定时任务 {task_id}")
logger.error(f"任务执行失败: {str(e)}")
logger.debug(f"任务状态更新: {task_status}")
```

### 4. 配置持久化
- JSON文件格式存储
- 确保文件目录存在
- 原子性写入操作
- 备份机制考虑

### 5. 时间处理
```python
from datetime import datetime, timedelta, time
import dateutil.parser

# 时间解析示例
target_time = datetime.strptime("2024-01-01 10:30:00", "%Y-%m-%d %H:%M:%S")
current_time = datetime.now()
time_diff = target_time - current_time
```

### 6. 优雅关闭
- 实现`__del__`方法
- 正确关闭调度器和线程
- 保存未完成的任务状态

## 模块集成示例

### 主程序初始化
```python
from src.AutoTasker.autoTasker import AutoTasker
from src.handlers.autosend import AutoSendHandler
from modules.reminder.service import ReminderService

# 初始化消息处理器
message_handler = MessageHandler(...)

# 初始化定时任务系统
auto_tasker = AutoTasker(message_handler)

# 初始化自动发送处理器
auto_send_handler = AutoSendHandler(message_handler, config, listen_list)

# 初始化提醒服务
reminder_service = ReminderService(message_handler, mem_service)

# 启动自动发送
auto_send_handler.start_countdown()
```

## 输出要求

### 代码质量标准
1. 遵循PEP 8编码规范
2. 包含完整的类型注解
3. 详细的docstring文档
4. 完整的异常处理
5. 充分的日志记录

### 文件结构
```
src/
├── AutoTasker/
│   └── autoTasker.py
├── handlers/
│   ├── autosend.py
│   └── message.py
└── modules/
    └── reminder/
        └── service.py
```

### 测试验证
实现完成后，请提供：
1. 基本功能测试用例
2. 异常情况处理验证
3. 并发安全性测试
4. 配置文件读写测试

## 风格要求
- 代码注释使用中文
- 变量命名使用英文，遵循snake_case
- 类名使用PascalCase
- 常量使用UPPER_CASE

## 受众
此模块将被有经验的Python开发者使用和维护，需要确保代码的可读性、可维护性和扩展性。

请严格按照以上完整代码实现，确保每个模块都能独立工作，同时保持良好的模块间协作能力。所有代码都已经过实际项目验证，可以直接使用。