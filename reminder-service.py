# Reminder Service - 完整的提醒服务实现
# 包含提醒识别、任务管理、提醒执行等完整功能

import logging
import threading
import time
import json
import ast
import os
import sys
from datetime import datetime
from typing import Dict, List, Optional
from openai import OpenAI

logger = logging.getLogger('main')

# ==================== 提醒识别服务 ====================

class ReminderRecognitionService:
    """提醒意图识别服务"""
    
    def __init__(self, llm_service):
        """
        初始化提醒识别服务
        
        Args:
            llm_service: LLM 服务实例
        """
        self.llm_service = llm_service
        self.intent_recognition_settings = {
            "api_key": "your_api_key",
            "base_url": "your_base_url", 
            "model": "your_model",
            "temperature": 0.1
        }
        
        self.client = OpenAI(
            api_key=self.intent_recognition_settings["api_key"],
            base_url=self.intent_recognition_settings["base_url"]
        )
        
        # 提醒识别系统提示词
        self.sys_prompt = """你是一个时间识别助手。你的任务只是分析消息中的时间信息，不需要回复用户。

判断标准：
1. 消息必须明确表达"提醒"、"叫我"、"记得"等提醒意图
2. 消息必须包含具体或相对的时间信息
3. 返回的时间必须是未来的时间点
4. 用户提到模糊的时间，比如我去洗个澡，吃个饭，不应该创建任务
5. 必须包含具体的时间和具体的提示内容才应该创建提示任务，否则返回：NOT_TIME_RELATED

若你发现有提醒任务，你必须严格返回类似于下面示例的列表，不要添加任何其他内容：
[
    {
        "target_time": "YYYY-MM-DD HH:mm:ss",
        "reminder_content": "提醒内容"
    }
]

注意事项：
1. 时间必须是24小时制
2. 日期格式必须是 YYYY-MM-DD
3. 如果只提到时间没提到日期，默认是今天或明天（取决于当前时间）
4. 相对时间（如"三分钟后"）需要转换为具体时间点
5. 时间点必须在当前时间之后
6. 如果不是提醒请求，只返回：NOT_TIME_RELATED"""

        # 示例数据
        self.examples = {
            "example-1": {
                "input": {
                    "role": "user",
                    "content": "时间：2024-03-16 17:39:00\n消息：三分钟后提醒我喝水，五分钟后提醒我吃饭"
                },
                "output": {
                    "role": "assistant",
                    "content": [
                        {
                            "target_time": "2024-03-16 17:42:00",
                            "reminder_content": "喝水"
                        },
                        {
                            "target_time": "2024-03-16 17:44:00",
                            "reminder_content": "吃饭"
                        }
                    ]
                }
            },
            "example-2": {
                "input": {
                    "role": "user",
                    "content": "时间：2024-04-18 07:39:00\n消息：我等下去洗个澡"
                },
                "output": {
                    "role": "assistant",
                    "content": "NOT_TIME_RELATED"
                }
            },
            "example-3": {
                "input": {
                    "role": "user",
                    "content": "时间：2025-02-09 14:49:00\n消息：五点提醒我吃晚饭可以吗？"
                },
                "output": {
                    "role": "assistant",
                    "content": [
                        {
                            "target_time": "2025-02-09 17:00:00",
                            "reminder_content": "吃晚饭"
                        }
                    ]
                }
            }
        }

    def recognize(self, message: str) -> Optional[str|List[Dict]]:
        """
        识别并提取消息中的提醒意图
        
        Args:
            message: 用户消息
            
        Returns:
            Optional[list]: 包含提醒任务的列表或"NOT_TIME_RELATED"
        """
        current_time = datetime.now()
        logger.info(f"调用模型进行提醒意图识别...")
        
        # 构建消息列表
        messages = [{"role": "system", "content": self.sys_prompt}]
        
        # 添加示例
        for example in self.examples.values():
            messages.append({
                "role": example["input"]["role"],
                "content": example["input"]["content"]
            })
            messages.append({
                "role": example["output"]["role"],
                "content": str(example["output"]["content"])
            })
        
        # 添加用户消息
        messages.append({
            "role": "user",
            "content": f"时间：{current_time.strftime('%Y-%m-%d %H:%M:%S')}\n消息：{message}"
        })

        request_config = {
            "model": self.intent_recognition_settings["model"],
            "messages": messages,
            "temperature": self.intent_recognition_settings["temperature"],
            "max_tokens": 1024,
        }

        while True:
            response = self.client.chat.completions.create(**request_config)
            response_content = response.choices[0].message.content
            
            # 预处理响应内容
            if response_content.startswith("```json") and response_content.endswith("```"):
                response_content = response_content[7:-3].strip()
            
            # 不包含定时提醒意图
            if response_content == "NOT_TIME_RELATED":
                return response_content
            
            try:
                response_content = ast.literal_eval(response_content)
                if isinstance(response_content, list):
                    return response_content
            except (ValueError, SyntaxError):
                logger.warning("识别定时任务意图失败，进行重试...")

# ==================== 提醒任务数据结构 ====================

class ReminderTask:
    """单个提醒任务结构"""
    
    def __init__(self, task_id: str, chat_id: str, target_time: datetime,
                 content: str, sender_name: str, reminder_type: str = "text"):
        self.task_id = task_id
        self.chat_id = chat_id
        self.target_time = target_time
        self.content = content
        self.sender_name = sender_name
        self.reminder_type = reminder_type
        self.audio_path = None

    def is_due(self) -> bool:
        """检查是否到达提醒时间"""
        return datetime.now() >= self.target_time

# ==================== 提醒服务主类 ====================

class ReminderService:
    """统一提醒服务"""
    
    def __init__(self, message_handler, mem_service):
        """
        初始化提醒服务
        
        Args:
            message_handler: 消息处理器实例
            mem_service: 内存服务实例
        """
        self.message_handler = message_handler
        self.mem_service = mem_service
        self.llm_service = message_handler.deepseek if hasattr(message_handler, 'deepseek') else None
        self.active_reminders: Dict[str, ReminderTask] = {}
        self._lock = threading.Lock()
        self._start_polling_thread()
        logger.info("统一提醒服务已启动")

    def _start_polling_thread(self):
        """启动后台轮询线程"""
        thread = threading.Thread(target=self._poll_reminders_loop, daemon=True)
        thread.start()

    def _poll_reminders_loop(self):
        """无限循环检查到期任务"""
        while True:
            due_tasks: List[ReminderTask] = []
            with self._lock:
                for _, task in list(self.active_reminders.items()):
                    if task.is_due():
                        due_tasks.append(task)
                for task in due_tasks:
                    del self.active_reminders[task.task_id]
            
            for task in due_tasks:
                logger.info(f"到达提醒时间，执行提醒: {task.task_id}")
                self._do_remind(task)
            
            time.sleep(1)

    def _do_remind(self, task: ReminderTask):
        """执行提醒任务"""
        try:
            prompt = self._get_reminder_prompt(task.content)
            logger.debug(f"生成提醒消息 - 用户: {task.sender_name}, 类型: {task.reminder_type}, 提示词: {prompt}")

            if task.reminder_type == "voice":
                # 语音提醒逻辑（需要根据实际情况实现）
                logger.info(f"执行语音提醒: {task.content}")
                # Call(wx=wx, who=task.sender_name, audio_file_path=task.audio_path)
            else:
                # 文本提醒
                self.message_handler.handle_user_message(
                    content=prompt,
                    chat_id=task.chat_id,
                    sender_name="System",
                    username="System",
                    is_group=False
                )
            
            logger.info(f"已发送提醒消息给 {task.sender_name}")
        except Exception as e:
            logger.error(f"发送提醒消息失败: {str(e)}")

    def _get_reminder_prompt(self, content: str) -> str:
        """
        生成提醒消息的提示词
        
        这是告诉LLM时间到了的核心提示词
        """
        return f"""现在提醒时间到了，用户之前设定的提示内容为"{content}"。请以你的人设中的身份主动找用户聊天。保持角色设定的一致性和上下文的连贯性"""

    def _remind_text_generate(self, remind_content: str, sender_name: str):
        """
        生成复杂的提醒文本（用于语音提醒预生成）
        
        这个方法包含完整的角色扮演上下文
        """
        if not self.llm_service or not self.mem_service:
            return self._get_reminder_prompt(remind_content)
            
        try:
            core_mem = self.mem_service.get_core_memory(
                avatar_name=self.message_handler.current_avatar, 
                user_id=sender_name
            )
            context = self.mem_service.get_recent_context(
                avatar_name=self.message_handler.current_avatar, 
                user_id=sender_name
            )
            
            sys_prompt = f"你将进行角色扮演，请你同用户进行符合人设的交流沟通。你的人设如下：\n\n{self.message_handler.prompt_content}\n\n"
            sys_prompt = sys_prompt + f"另外，作为一个仿真的角色扮演者，你需要掌握一些你不一定用到的、但是十分重要的知识：{core_mem}。你的每次回应都不应该违反这些知识！"
            
            messages = [{"role": "system", "content": sys_prompt}, *context[-self.message_handler.max_groups * 2:]]
            
            # 关键提示词：告诉LLM时间到了
            reminder_prompt = f"现在提醒时间到了，用户之前设定的提示内容为"{remind_content}"。请以你的人设中的身份主动找用户聊天。保持角色设定的一致性和上下文的连贯性。"
            messages.append({"role": "system", "content": reminder_prompt})
            
            request_config = {
                "model": self.message_handler.model,
                "messages": messages,
                "temperature": self.message_handler.temperature,
                "max_tokens": self.message_handler.max_token,
            }
            
            response = self.llm_service.client.chat.completions.create(**request_config)
            raw_content = response.choices[0].message.content
            return raw_content
        except Exception as e:
            logger.error(f"生成提醒文本失败: {str(e)}")
            return self._get_reminder_prompt(remind_content)

    def add_reminder(self, chat_id: str, target_time: datetime, content: str, 
                    sender_name: str, reminder_type: str = "text"):
        """添加提醒任务"""
        try:
            task_id = f"reminder_{chat_id}_{datetime.now().timestamp()}"
            task = ReminderTask(task_id, chat_id, target_time, content, sender_name, reminder_type)
            
            if reminder_type == "voice":
                logger.info("检测到语音提醒任务，预生成回复中")
                remind_text = self._remind_text_generate(remind_content=content, sender_name=sender_name)
                logger.info(f"预生成回复: {remind_text}")
                # 这里可以添加TTS生成逻辑
                # audio_file_path = tts._generate_audio_file(remind_text)
                # task.audio_path = audio_file_path
            
            with self._lock:
                self.active_reminders[task_id] = task
            
            logger.info(f"提醒任务已添加。提醒时间: {target_time}, 内容: {content}，用户：{sender_name}，类型：{reminder_type}")
        except Exception as e:
            logger.error(f"添加提醒任务失败: {str(e)}")

    def cancel_reminder(self, task_id: str) -> bool:
        """取消提醒任务"""
        with self._lock:
            if task_id in self.active_reminders:
                del self.active_reminders[task_id]
                logger.info(f"提醒任务已取消: {task_id}")
                return True
            return False

    def list_reminders(self) -> List[Dict]:
        """获取所有活跃提醒任务列表"""
        with self._lock:
            return [{
                'task_id': task_id,
                'chat_id': task.chat_id,
                'target_time': task.target_time.isoformat(),
                'content': task.content,
                'sender_name': task.sender_name,
                'reminder_type': task.reminder_type
            } for task_id, task in self.active_reminders.items()]

# ==================== 使用示例 ====================

if __name__ == '__main__':
    """
    使用示例和测试代码
    """
    
    # 模拟消息处理器
    class MockMessageHandler:
        def __init__(self):
            self.current_avatar = "test_avatar"
            self.prompt_content = "你是一个友好的AI助手"
            self.model = "gpt-3.5-turbo"
            self.temperature = 0.7
            self.max_token = 1024
            self.max_groups = 5
        
        def handle_user_message(self, content, chat_id, sender_name, username, is_group):
            print(f"发送提醒消息到 {chat_id}: {content}")
    
    # 模拟内存服务
    class MockMemoryService:
        def get_core_memory(self, avatar_name, user_id):
            return "核心记忆信息"
        
        def get_recent_context(self, avatar_name, user_id):
            return [{"role": "user", "content": "测试消息"}]
    
    # 测试提醒识别
    print("=== 测试提醒识别 ===")
    # recognition_service = ReminderRecognitionService(None)
    # result = recognition_service.recognize("三分钟后提醒我喝水")
    # print(f"识别结果: {result}")
    
    # 测试提醒服务
    print("=== 测试提醒服务 ===")
    message_handler = MockMessageHandler()
    mem_service = MockMemoryService()
    reminder_service = ReminderService(message_handler, mem_service)
    
    # 添加一个测试提醒（1秒后触发）
    test_time = datetime.now().replace(second=datetime.now().second + 2)
    reminder_service.add_reminder(
        chat_id="test_chat",
        target_time=test_time,
        content="测试提醒内容",
        sender_name="test_user"
    )
    
    print("等待提醒触发...")
    time.sleep(5)
    print("测试完成")
