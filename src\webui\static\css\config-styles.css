/* 配置页面样式文件 */

:root {
    --primary-color: #6366f1;
    --secondary-color: #4f46e5;
    --background-color: #f8fafc;
    --text-color: #1e293b;
    --card-bg: rgba(255, 255, 255, 0.8);
    --card-border: rgba(255, 255, 255, 0.5);
    --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

[data-bs-theme="dark"] {
    --primary-color: #818cf8;
    --secondary-color: #6366f1;
    --background-color: #0f172a;
    --text-color: #e2e8f0;
    --card-bg: rgba(30, 41, 59, 0.8);
    --card-border: rgba(255, 255, 255, 0.1);
    --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

html, body {
    height: 100%;
    margin: 0;
    padding: 0;
}

body {
    background: var(--background-color);
    color: var(--text-color);
    transition: all 0.3s ease;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

main {
    flex: 1 0 auto;
    width: 100%;
    padding: 2rem 0;
}

/* 配置区域样式 */
.config-section {
    background: var(--card-bg);
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    border-radius: 1rem;
    border: 1px solid var(--card-border);
    box-shadow: var(--card-shadow);
    padding: 2rem;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    opacity: 0;
    transform: translateY(20px);
    animation: slideUp 0.5s ease forwards;
    animation-delay: 0.1s;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.config-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    opacity: 0.5;
}

/* 表单控件样式 */
.form-control, .form-select {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    border: 1px solid var(--card-border) !important;
    background: var(--card-bg);
    color: var(--text-color);
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 0.25rem rgba(99, 102, 241, 0.25);
    outline: none;
    transform: translateY(-2px);
}

.form-label {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.form-label:hover {
    color: var(--primary-color);
}

/* 徽章样式 */
.badge-info {
    background: var(--primary-color);
    cursor: pointer;
}

.badge {
    transition: all 0.3s ease;
}

.badge:hover {
    transform: scale(1.1);
}

/* 手风琴样式 */
.accordion-button {
    background: transparent !important;
    border: none;
}

.accordion-button:not(.collapsed) {
    background: rgba(var(--bs-primary-rgb), 0.1);
    color: var(--primary-color);
}

.accordion-item {
    background: transparent;
    border-color: var(--card-border);
}

/* 导航栏样式 */
.navbar {
    background: var(--card-bg) !important;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--card-border);
}

/* 输入框组样式 */
.input-group {
    border: 1px solid var(--card-border);
    border-radius: 0.5rem;
    overflow: hidden;
}

.input-group .form-control {
    border: none !important;
}

/* 配置项容器样式 */
.config-item {
    margin-bottom: 1.5rem;
    padding: 1rem;
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.config-item:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 列表项样式 */
.list-group-item {
    border: none;
    margin-bottom: 8px;
    border-radius: 8px !important;
    transition: all 0.3s ease;
}

.list-group-item:hover {
    transform: translateX(5px);
    background: rgba(var(--bs-primary-rgb), 0.1);
}

/* 按钮样式 */
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
}

.btn-outline-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* 滑块样式 */
.temperature-slider {
    -webkit-appearance: none;
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: linear-gradient(to right,
        rgb(13, 110, 253) 0%,
        rgb(13, 202, 240) 50%,
        rgb(253, 126, 20) 100%);
    outline: none;
    transition: opacity 0.2s;
}

.temperature-slider::-webkit-slider-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--card-bg);
    border: 2px solid rgb(13, 110, 253);
    cursor: pointer;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.temperature-slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
}

.temperature-value {
    transition: all 0.3s ease;
}

.temperature-value.updating {
    color: var(--primary-color);
    transform: scale(1.2);
}

.queue-timeout-slider {
    -webkit-appearance: none;
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: linear-gradient(to right,
        rgb(253, 20, 20) 0%,
        rgb(253, 126, 20) 40%,
        rgb(13, 202, 240) 60%,
        rgb(13, 110, 253) 100%);
    outline: none;
    transition: opacity 0.2s;
}

.queue-timeout-slider::-webkit-slider-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--card-bg);
    border: 2px solid rgb(13, 110, 253);
    cursor: pointer;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.queue-timeout-slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
}

/* 通知样式 */
.toast {
    border-radius: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    transform: translateY(-20px);
    opacity: 0;
}

.toast.show {
    transform: translateY(0);
    opacity: 1;
}

.notification-container {
    z-index: 1050;
}

/* 响应式设计 */
@media (max-width: 767px) {
    .config-section {
        padding: 1rem;
        margin-bottom: 0;
    }
    
    .col-md-6 {
        margin-bottom: 2rem;
    }
    
    main.container-fluid {
        padding: 2rem 1rem;
        padding-bottom: 5rem;
    }
}

@media (min-width: 768px) {
    .col-md-6.pe-md-2 {
        padding-right: 1.5rem !important;
    }
    
    .col-md-6.ps-md-2 {
        padding-left: 1.5rem !important;
    }
    
    main.container-fluid {
        padding: 2rem;
        padding-bottom: 5rem;
    }
}

/* 日期选择按钮组响应式样式 */
.btn-group.flex-wrap {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
}

.btn-group.flex-wrap .btn {
    flex: 1 1 calc(14.28% - 0.25rem);
    min-width: 40px;
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
    text-align: center;
}

@media (max-width: 768px) {
    .btn-group.flex-wrap .btn {
        flex: 1 1 calc(33.33% - 0.25rem);
        min-width: 30px;
        padding: 0.25rem 0.375rem;
        font-size: 0.75rem;
    }
}

@media (max-width: 480px) {
    .btn-group.flex-wrap .btn {
        flex: 1 1 calc(50% - 0.25rem);
        min-width: 25px;
        padding: 0.25rem;
        font-size: 0.75rem;
    }
}

/* 特殊列表样式 */
#schedule-settings .list-group-item {
    background: rgba(30, 41, 59, 0.5);
    color: #fff;
}

#schedule-settings .list-group-item:hover {
    background: rgba(30, 41, 59, 0.8) !important;
    transform: translateX(5px);
}

#selected_users_LISTEN_LIST .list-group-item {
    background: var(--bs-list-group-bg);
    color: var(--bs-body-color);
}

[data-bs-theme="dark"] #selected_users_LISTEN_LIST .list-group-item {
    background: rgba(30, 41, 59, 0.5);
    color: #fff;
}

/* 输入框过渡效果 */
#customApiInput {
    transition: all 0.3s ease;
}

#customApiInput.show {
    transform: translateY(0);
    opacity: 1;
}

#customApiInput.hide {
    transform: translateY(-10px);
    opacity: 0;
}