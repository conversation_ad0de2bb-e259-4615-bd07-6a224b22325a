<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KouriChat - 角色设定</title>
    <link href="/static/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="/static/mom.ico">
    <script src="/static/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/dark-mode.js"></script>
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #4f46e5;
            --background-color: #f8fafc;  /* 统一背景颜色 */
            --text-color: #1e293b;
            --card-bg: rgba(255, 255, 255, 0.8); /* 统一卡片背景 */
            --card-border: rgba(255, 255, 255, 0.5); /* 统一卡片边框 */
            --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); /* 统一卡片阴影 */
        }

        [data-bs-theme="dark"] {
            --primary-color: #818cf8;
            --secondary-color: #6366f1;
            --background-color: #0f172a;  /* 统一深色背景 */
            --text-color: #e2e8f0;        /* 浅色文字 */
            --card-bg: rgba(30, 41, 59, 0.8); /* 统一深色卡片背景 */
            --card-border: rgba(255, 255, 255, 0.1); /* 统一深色卡片边框 */
            --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1); /* 统一深色卡片阴影 */
        }

        body {
            background: var(--background-color);
            color: var(--text-color);
            transition: all 0.3s ease;
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            min-height: 100vh;
        }

        .container {
            margin: auto;
        }

        .glass-panel {
            background: var(--card-bg);
            border-radius: 1rem;
            border: 1px solid var(--card-border);
            box-shadow: var(--card-shadow);
            padding: 2rem;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
        }

        .form-label {
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-color);
        }

        .form-control {
            background: var(--card-bg);
            color: var(--text-color);
            border-color: var(--card-border);
            transition: all 0.3s ease;
            min-height: 100px;
            resize: vertical;
            overflow-y: hidden;
        }

        .form-control:focus {
            background: var(--card-bg);
            color: var(--text-color);
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(99, 102, 241, 0.25);
        }

        .form-control::placeholder {
            color: var(--text-color);
            opacity: 0.65;
        }

        /* 暗色模式下的按钮样式 */
        [data-bs-theme="dark"] .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        [data-bs-theme="dark"] .btn-primary:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        /* 保存按钮样式 */
        .btn-save {
            width: 100%; /* 占据100%宽度 */
            padding: 0.75rem; /* 按钮内边距 */
            margin-top: 1rem; /* 按钮上边距 */
            background-color: var(--primary-color); /* 背景颜色 */
            color: white; /* 字体颜色 */
            border: none; /* 无边框 */
            border-radius: 0.5rem; /* 圆角 */
            transition: background-color 0.3s ease; /* 背景颜色过渡 */
        }

        .btn-save:hover {
            background-color: var(--secondary-color); /* 悬停时的背景颜色 */
        }
        
        /* 人设选择器样式 */
        .avatar-selector {
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            background: var(--card-bg);
            border-radius: 0.5rem;
            padding: 1rem;
            border: 1px solid var(--card-border);
            flex-wrap: wrap; /* 允许在小屏幕上换行 */
        }
        
        .avatar-selector select {
            flex-grow: 1;
            min-width: 200px; /* 确保下拉框有足够宽度 */
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: 1px solid var(--card-border);
            background: var(--card-bg);
            color: var(--text-color);
            transition: all 0.3s ease;
            font-size: 1rem;
            height: 42px;
        }
        
        .avatar-selector button {
            height: 42px;
            display: flex;
            align-items: center;
            justify-content: center;
            white-space: nowrap;
        }
        
        .avatar-selector select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(99, 102, 241, 0.25);
            outline: none;
        }
        
        .avatar-selector label {
            font-weight: 500;
            margin-bottom: 0;
            display: flex;
            align-items: center;
            white-space: nowrap;
            font-size: 1rem;
        }
        
        .avatar-selector label i {
            margin-right: 0.5rem;
            font-size: 1.2rem;
        }
        
        /* 添加加载动画 */
        .loading {
            position: relative;
        }
        
        .loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(2px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            border-radius: 1rem;
        }
        
        .loading::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s linear infinite;
            z-index: 1001;
        }
        
        @keyframes spin {
            to {
                transform: translate(-50%, -50%) rotate(360deg);
            }
        }

        /* 添加导航栏样式，与dashboard.html保持一致 */
        .navbar {
            background: var(--card-bg) !important;
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--card-border);
        }

        /* 添加深色模式下的模态框样式 */
        [data-bs-theme="dark"] .modal-content {
            background-color: #1a1f2e;
            color: #f8fafc;
        }
        
        [data-bs-theme="dark"] .modal-header,
        [data-bs-theme="dark"] .modal-footer {
            border-color: #2d3748;
        }
        
        [data-bs-theme="dark"] pre {
            background-color: #2d3748;
            color: #e2e8f0;
            border: 1px solid #4a5568;
        }
        
        /* 确保深色模式下的按钮有足够的对比度 */
        [data-bs-theme="dark"] .btn-outline-secondary {
            color: #e2e8f0;
            border-color: #4a5568;
        }
        
        [data-bs-theme="dark"] .btn-outline-secondary:hover {
            background-color: #4a5568;
            color: #f8fafc;
        }
        
        /* 确保深色模式下的表单元素有足够的对比度 */
        [data-bs-theme="dark"] .form-control::placeholder {
            color: #a0aec0;
        }
    </style>
</head>
<body>
    {% include 'navbar.html' %}
    
    <div class="container py-4">
        <div class="glass-panel mb-4">
            <h2>角色设定</h2>
            
            <!-- 添加用户选择器 -->
            <div class="alert alert-info mb-3">
                <div class="d-flex align-items-center flex-wrap">
                    <div class="me-3 mb-2">
                        <i class="bi bi-person-circle me-2"></i>选择用户：
                    </div>
                    <div class="me-auto mb-2">
                        <select id="userSelector" class="form-select form-select-sm">
                            <!-- 这里的选项将通过JavaScript动态加载 -->
                        </select>
                    </div>
                    <div class="mb-2">
                        <button type="button" class="btn btn-sm btn-outline-primary" id="refreshUsersBtn" title="刷新用户列表">
                            <i class="bi bi-arrow-clockwise"></i> 刷新用户列表
                        </button>
                    </div>
                </div>
                <div class="small mt-1">
                    <i class="bi bi-info-circle me-1"></i>请选择要编辑记忆的特定用户。每个用户有独立的记忆存储。
                </div>
            </div>
            
            <!-- 添加获取人设按钮 -->
            <div class="d-flex justify-content-end mb-3">
                <button type="button" class="btn btn-lg btn-info" id="getAvatarBtn" title="获取人设">
                    <i class="bi bi-download me-2"></i>获取人设
                </button>
            </div>
            
            <!-- 添加人设选择器 -->
            <div class="avatar-selector">
                <label for="avatarSelector"><i class="bi bi-person-badge"></i>选择人设:</label>
                <select id="avatarSelector" class="form-select">
                    <!-- 这里的选项将通过JavaScript动态加载 -->
                </select>
                <button type="button" class="btn btn-outline-primary" id="newAvatarBtn" title="创建新人设">
                    <i class="bi bi-plus-circle"></i> 新建人设
                </button>
                <button type="button" class="btn btn-outline-danger" id="deleteAvatarBtn" title="删除当前人设">
                    <i class="bi bi-trash"></i> 删除人设
                </button>
            </div>
            
            <form id="avatarForm">
                <div class="glass-panel mb-4">
                    <label for="task" class="form-label">任务</label>
                    <textarea class="form-control" id="task" name="task" rows="5" 
                        placeholder="请输入任务描述" title="任务描述" aria-label="任务描述"></textarea>
                </div>
                <div class="glass-panel mb-4">
                    <label for="role" class="form-label">角色</label>
                    <textarea class="form-control" id="role" name="role" rows="5" 
                        placeholder="请输入角色设定" title="角色设定" aria-label="角色设定"></textarea>
                </div>
                <div class="glass-panel mb-4">
                    <label for="appearance" class="form-label">外表</label>
                    <textarea class="form-control" id="appearance" name="appearance" rows="5" 
                        placeholder="请输入外表描述" title="外表描述" aria-label="外表描述"></textarea>
                </div>
                <div class="glass-panel mb-4">
                    <label for="experience" class="form-label">经历</label>
                    <textarea class="form-control" id="experience" name="experience" rows="5" 
                        placeholder="请输入经历描述" title="经历描述" aria-label="经历描述"></textarea>
                </div>
                <div class="glass-panel mb-4">
                    <label for="personality" class="form-label">性格</label>
                    <textarea class="form-control" id="personality" name="personality" rows="5" 
                        placeholder="请输入性格描述" title="性格描述" aria-label="性格描述"></textarea>
                </div>
                <div class="glass-panel mb-4">
                    <label for="classic_lines" class="form-label">经典台词</label>
                    <textarea class="form-control" id="classic_lines" name="classic_lines" rows="5" 
                        placeholder="请输入经典台词" title="经典台词" aria-label="经典台词"></textarea>
                </div>
                <div class="glass-panel mb-4">
                    <label for="preferences" class="form-label">喜好</label>
                    <textarea class="form-control" id="preferences" name="preferences" rows="5" 
                        placeholder="请输入喜好描述" title="喜好描述" aria-label="喜好描述"></textarea>
                </div>
                <div class="glass-panel mb-4">
                    <label for="notes" class="form-label">备注</label>
                    <textarea class="form-control" id="notes" name="notes" rows="5" 
                        placeholder="请输入备注信息" title="备注信息" aria-label="备注信息"></textarea>
                </div>
                <button type="button" class="btn btn-save" id="saveButton" title="保存角色设定">
                    <i class="bi bi-save me-2"></i>保存
                </button>
            </form>
        </div>
    </div>

    <!-- 新建人设模态框 -->
    <div class="modal fade" id="newAvatarModal" tabindex="-1" aria-labelledby="newAvatarModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="newAvatarModalLabel">新建人设</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="newAvatarName" class="form-label">人设名称</label>
                        <input type="text" class="form-control" id="newAvatarName" placeholder="请输入人设名称（英文）" required>
                        <small class="text-muted">请使用英文字母、数字和下划线，例如：ATRI、Mono_Chan</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="createAvatarBtn">创建</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加删除确认模态框 -->
    <div class="modal fade" id="deleteAvatarModal" tabindex="-1" aria-labelledby="deleteAvatarModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteAvatarModalLabel">删除人设</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    确定要删除这个人设吗？此操作不可恢复。
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 获取人设模态框 -->
    <div class="modal fade" id="getAvatarModal" tabindex="-1" aria-labelledby="getAvatarModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="getAvatarModalLabel">获取人设</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>请选择获取人设的方式：</p>
                    <div class="d-grid gap-2">
                        <a href="https://avatars.kourichat.com/#/" class="btn btn-primary" target="_blank">
                            前往官网下载
                        </a>
                        <a href="https://jq.qq.com/?_wv=1027&k=5F4Gz5K" class="btn btn-secondary" target="_blank">
                            加入QQ群 715616260 下载
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 当前选中的人设目录
            let currentAvatar = 'MONO';
            // 当前选中的用户ID
            let currentUser = 'default';
            let rawContent = ''; // 存储原始内容
            
            // 初始化背景
            fetch('/get_background')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success' && data.path) {
                        document.body.style.backgroundImage = `url('${data.path}')`;
                    }
                })
                .catch(error => console.error('Error:', error));
            
            // 获取可用的人设列表
            function loadAvatarList() {
                fetch('/get_available_avatars')
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            const selector = document.getElementById('avatarSelector');
                            selector.innerHTML = ''; // 清空现有选项
                            
                            // 添加人设选项
                            data.avatars.forEach(avatar => {
                                const option = document.createElement('option');
                                // 从路径中提取人设名称
                                const avatarName = avatar.split('/').pop();
                                option.value = avatarName;
                                option.textContent = avatarName;
                                selector.appendChild(option);
                            });
                            
                            // 设置默认选中项
                            if (data.avatars.length > 0) {
                                // 尝试从localStorage获取上次选择的人设
                                const lastSelected = localStorage.getItem('lastSelectedAvatar');
                                if (lastSelected && data.avatars.some(a => a.includes(lastSelected))) {
                                    selector.value = lastSelected;
                                    currentAvatar = lastSelected;
                                } else {
                                    // 默认选择第一个
                                    const firstAvatar = data.avatars[0].split('/').pop();
                                    selector.value = firstAvatar;
                                    currentAvatar = firstAvatar;
                                }
                                
                                // 加载选中人设的内容
                                loadAvatarContent(currentAvatar);
                                // 加载用户列表
                                loadUserList(currentAvatar);
                            }
                        } else {
                            console.error('加载人设列表失败:', data.message);
                            showToast('加载人设列表失败: ' + data.message, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('加载人设列表失败:', error);
                        showToast('加载人设列表失败: ' + error, 'error');
                    });
            }
            
            // 获取当前角色的用户列表
            function loadUserList(avatarName) {
                fetch(`/get_avatar_users?avatar=${avatarName}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            const selector = document.getElementById('userSelector');
                            selector.innerHTML = ''; // 清空现有选项
                            
                            // 添加用户选项
                            data.users.forEach(user => {
                                const option = document.createElement('option');
                                option.value = user;
                                option.textContent = user;
                                selector.appendChild(option);
                            });
                            
                            // 设置默认选中项
                            if (data.users.length > 0) {
                                // 尝试从localStorage获取上次选择的用户
                                const lastSelectedUser = localStorage.getItem(`lastSelectedUser_${avatarName}`);
                                if (lastSelectedUser && data.users.includes(lastSelectedUser)) {
                                    selector.value = lastSelectedUser;
                                    currentUser = lastSelectedUser;
                                } else {
                                    // 默认选择第一个
                                    selector.value = data.users[0];
                                    currentUser = data.users[0];
                                }
                                
                                // 保存当前选择
                                localStorage.setItem(`lastSelectedUser_${avatarName}`, currentUser);
                            }
                        } else {
                            console.error('加载用户列表失败:', data.message);
                            showToast('加载用户列表失败: ' + data.message, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('加载用户列表失败:', error);
                        showToast('加载用户列表失败: ' + error, 'error');
                    });
            }
            
            // 加载指定人设的内容
            function loadAvatarContent(avatarName) {
                fetch(`/load_avatar_content?avatar=${avatarName}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            // 保存原始内容
                            rawContent = data.raw_content || '';
                            
                            // 更新所有文本框的内容
                            const fields = ['task', 'role', 'appearance', 'experience', 
                                          'personality', 'classic_lines', 'preferences', 'notes'];
                            
                            fields.forEach(field => {
                                const element = document.getElementById(field);
                                if (element) {
                                    element.value = data.content[field] || '';
                                    // 自动调整文本框高度以适应内容
                                    element.style.height = 'auto';
                                    element.style.height = (element.scrollHeight) + 'px';
                                }
                            });
                            
                            // 保存当前选择的人设到localStorage
                            localStorage.setItem('lastSelectedAvatar', avatarName);
                            currentAvatar = avatarName;
                            
                            // 显示加载成功提示
                            showToast(`已加载 ${avatarName} 的角色设定`, 'success');
                            
                            // 加载用户列表
                            loadUserList(currentAvatar);
                        } else {
                            console.error('加载人设内容失败:', data.message);
                            showToast('加载人设内容失败: ' + data.message, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('加载人设内容失败:', error);
                        showToast('加载人设内容失败: ' + error, 'error');
                    });
            }
            
            // 修改查看原始人设内容的功能
            function addViewRawButton() {
                // 在人设选择器旁添加一个按钮
                const selector = document.querySelector('.avatar-selector');
                const viewRawBtn = document.createElement('button');
                viewRawBtn.className = 'btn btn-outline-secondary';
                viewRawBtn.innerHTML = '<i class="bi bi-markdown"></i> 原始人设内容';
                viewRawBtn.title = '查看原始Markdown内容';
                viewRawBtn.addEventListener('click', function() {
                    // 创建模态框
                    const modalDiv = document.createElement('div');
                    modalDiv.className = 'modal fade';
                    modalDiv.id = 'rawContentModal';
                    modalDiv.tabIndex = '-1';
                    modalDiv.setAttribute('aria-labelledby', 'rawContentModalLabel');
                    modalDiv.setAttribute('aria-hidden', 'true');
                    
                    modalDiv.innerHTML = `
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="rawContentModalLabel">${currentAvatar} - 原始Markdown内容</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-end mb-2">
                                            <button type="button" class="btn btn-sm btn-outline-secondary me-2" id="copyMdBtn">
                                                <i class="bi bi-clipboard"></i> 复制
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" id="clearMdBtn">
                                                <i class="bi bi-trash"></i> 清空
                                            </button>
                                        </div>
                                        <textarea class="form-control" 
                                            id="rawMdContent" rows="15"
                                            style="min-height: 400px; max-height: 60vh; resize: vertical; overflow-y: auto; font-family: monospace;"
                                        >${rawContent}</textarea>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                    <button type="button" class="btn btn-primary" id="saveMdBtn">保存修改</button>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    document.body.appendChild(modalDiv);
                    
                    // 显示模态框
                    const modal = new bootstrap.Modal(modalDiv);
                    modal.show();
                    
                    // 复制按钮功能
                    document.getElementById('copyMdBtn').addEventListener('click', function() {
                        const textarea = document.getElementById('rawMdContent');
                        textarea.select();
                        document.execCommand('copy');
                        showToast('内容已复制到剪贴板', 'success');
                    });
                    
                    // 清空按钮功能
                    document.getElementById('clearMdBtn').addEventListener('click', function() {
                        if(confirm('确定要清空所有内容吗？此操作不可恢复。')) {
                            document.getElementById('rawMdContent').value = '';
                            showToast('内容已清空', 'success');
                        }
                    });
                    
                    // 保存修改按钮功能
                    document.getElementById('saveMdBtn').addEventListener('click', function() {
                        const newContent = document.getElementById('rawMdContent').value;
                        const avatarData = {
                            avatar: currentAvatar,
                            content: newContent
                        };
                        
                        fetch('/save_avatar_raw', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(avatarData)
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.status === 'success') {
                                showToast('修改已保存', 'success');
                                modal.hide();
                                // 重新加载内容
                                loadAvatarContent(currentAvatar);
                            } else {
                                showToast('保存失败: ' + data.message, 'error');
                            }
                        })
                        .catch(error => {
                            showToast('保存失败: ' + error, 'error');
                        });
                    });
                    
                    // 模态框关闭后移除DOM
                    modalDiv.addEventListener('hidden.bs.modal', function() {
                        document.body.removeChild(modalDiv);
                    });
                });
                
                // 添加编辑核心记忆按钮
                const editCoreMemoryBtn = document.createElement('button');
                editCoreMemoryBtn.className = 'btn btn-outline-secondary';
                editCoreMemoryBtn.innerHTML = '<i class="bi bi-database-fill"></i> 编辑核心记忆';
                editCoreMemoryBtn.title = '编辑角色的核心记忆';
                editCoreMemoryBtn.addEventListener('click', function() {
                    // 获取当前选中的角色名称和用户ID
                    const currentAvatar = document.getElementById('avatarSelector').value;
                    const currentUser = document.getElementById('userSelector').value;
                    
                    // 加载核心记忆内容
                    fetch(`/load_core_memory?avatar=${currentAvatar}&user_id=${currentUser}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.status === 'success') {
                                // 创建一个模态框来编辑核心记忆
                                const modalHTML = `
                                <div class="modal fade" id="coreMemoryModal" tabindex="-1" aria-labelledby="coreMemoryModalLabel" aria-hidden="true">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="coreMemoryModalLabel">${currentAvatar} - 用户 ${currentUser} 的核心记忆编辑</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="mb-3">
                                                    <label for="coreMemoryContent" class="form-label">核心记忆内容</label>
                                                    <div class="alert alert-info mb-2">
                                                        <i class="bi bi-info-circle-fill"></i> 核心记忆包含角色对用户的长期印象和关键信息，会在每次对话中被使用。您可以在这里编辑角色记住的重要内容。
                                                    </div>
                                                    <textarea class="form-control" id="coreMemoryContent" rows="8" 
                                                        style="min-height: 200px; max-height: 50vh; resize: vertical; overflow-y: auto;">${data.content || ''}</textarea>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                <button type="button" class="btn btn-danger" id="clearCoreMemoryBtn">清空核心记忆</button>
                                                <button type="button" class="btn btn-primary" id="saveCoreMemoryBtn">保存</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                `;
                                
                                // 添加模态框到body
                                document.body.insertAdjacentHTML('beforeend', modalHTML);
                                
                                // 显示模态框
                                const modal = new bootstrap.Modal(document.getElementById('coreMemoryModal'));
                                modal.show();
                                
                                // 保存按钮点击事件
                                document.getElementById('saveCoreMemoryBtn').addEventListener('click', function() {
                                    const memoryContent = document.getElementById('coreMemoryContent').value;
                                    
                                    // 保存核心记忆
                                    fetch('/save_core_memory', {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json',
                                        },
                                        body: JSON.stringify({
                                            avatar: currentAvatar,
                                            user_id: currentUser,
                                            content: memoryContent
                                        })
                                    })
                                    .then(response => response.json())
                                    .then(data => {
                                        if (data.status === 'success') {
                                            showToast('核心记忆保存成功', 'success');
                                            modal.hide();
                                            
                                            // 移除模态框
                                            document.getElementById('coreMemoryModal').remove();
                                        } else {
                                            showToast('保存失败: ' + data.message, 'error');
                                        }
                                    })
                                    .catch(error => {
                                        showToast('保存失败: ' + error, 'error');
                                    });
                                });
                                
                                // 清空核心记忆按钮点击事件
                                document.getElementById('clearCoreMemoryBtn').addEventListener('click', function() {
                                    if (confirm('确定要清空核心记忆吗？此操作无法撤销！')) {
                                        fetch('/clear_core_memory', {
                                            method: 'POST',
                                            headers: {
                                                'Content-Type': 'application/json',
                                            },
                                            body: JSON.stringify({
                                                avatar: currentAvatar,
                                                user_id: currentUser
                                            })
                                        })
                                        .then(response => response.json())
                                        .then(data => {
                                            if (data.status === 'success') {
                                                showToast('核心记忆已清空', 'success');
                                                document.getElementById('coreMemoryContent').value = '';
                                            } else {
                                                showToast('清空失败: ' + data.message, 'error');
                                            }
                                        })
                                        .catch(error => {
                                            showToast('清空失败: ' + error, 'error');
                                        });
                                    }
                                });
                                
                                // 模态框关闭事件
                                document.getElementById('coreMemoryModal').addEventListener('hidden.bs.modal', function() {
                                    this.remove();
                                });
                            } else {
                                showToast('加载核心记忆失败: ' + data.message, 'error');
                            }
                        })
                        .catch(error => {
                            showToast('加载核心记忆失败: ' + error, 'error');
                        });
                });
                
                // 添加编辑短期记忆按钮
                const editShortMemoryBtn = document.createElement('button');
                editShortMemoryBtn.className = 'btn btn-outline-secondary';
                editShortMemoryBtn.innerHTML = '<i class="bi bi-chat-dots"></i> 编辑短期记忆';
                editShortMemoryBtn.title = '编辑角色的短期记忆对话历史';
                editShortMemoryBtn.addEventListener('click', function() {
                    // 获取当前选中的角色名称和用户ID
                    const currentAvatar = document.getElementById('avatarSelector').value;
                    const currentUser = document.getElementById('userSelector').value;
                    
                    // 加载短期记忆内容
                    fetch(`/load_short_memory?avatar=${currentAvatar}&user_id=${currentUser}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.status === 'success') {
                                const conversations = data.conversations || [];
                                
                                // 创建一个模态框来编辑短期记忆
                                const modalHTML = `
                                <div class="modal fade" id="shortMemoryModal" tabindex="-1" aria-labelledby="shortMemoryModalLabel" aria-hidden="true">
                                    <div class="modal-dialog modal-xl">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="shortMemoryModalLabel">${currentAvatar} - 用户 ${currentUser} 的短期记忆对话编辑</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="mb-3">
                                                    <div class="alert alert-info">
                                                        <i class="bi bi-info-circle-fill"></i> 短期记忆包含最近的对话历史，可以逐条编辑、删除或添加对话。
                                                    </div>
                                                    
                                                    <div class="d-flex justify-content-end mb-3">
                                                        <button type="button" class="btn btn-sm btn-success me-2" id="addConversationBtn">
                                                            <i class="bi bi-plus-circle"></i> 添加对话
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-danger" id="clearAllConversationsBtn">
                                                            <i class="bi bi-trash"></i> 清空所有对话
                                                        </button>
                                                    </div>
                                                    
                                                    <div id="conversationsList" class="border rounded p-3" style="max-height: 60vh; overflow-y: auto;">
                                                        <div id="noConversationsMsg" class="text-center p-3 text-muted" ${conversations.length > 0 ? 'style="display:none;"' : ''}>
                                                            <i class="bi bi-chat-square-text"></i> 暂无对话记录
                                                        </div>
                                                        <div id="conversationsContainer">
                                                            <!-- 对话内容由JavaScript动态生成 -->
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                <button type="button" class="btn btn-primary" id="saveShortMemoryBtn">保存所有修改</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                `;
                                
                                // 添加模态框到body
                                document.body.insertAdjacentHTML('beforeend', modalHTML);
                                
                                // 显示模态框
                                const modal = new bootstrap.Modal(document.getElementById('shortMemoryModal'));
                                modal.show();
                                
                                // 保存所有修改
                                let conversationsData = [...conversations]; // 复制一份数据用于编辑
                                let editingIndex = -1; // 初始化编辑索引变量
                                
                                // 获取当前时间戳
                                function getCurrentTimestamp() {
                                    const now = new Date();
                                    return now.toISOString().replace('T', ' ').substring(0, 19);
                                }
                                
                                // 添加新对话
                                document.getElementById('addConversationBtn').addEventListener('click', function() {
                                    // 添加一个新的空对话
                                    conversationsData.push({
                                        timestamp: getCurrentTimestamp(),
                                        user: '',
                                        bot: ''
                                    });
                                    
                                    // 更新UI
                                    updateConversationsList();
                                    
                                    // 自动滚动到底部
                                    const container = document.getElementById('conversationsList');
                                    container.scrollTop = container.scrollHeight;
                                });
                                
                                // 更新对话列表UI
                                function updateConversationsList() {
                                    const container = document.getElementById('conversationsContainer');
                                    const noMsgElement = document.getElementById('noConversationsMsg');
                                    
                                    // 如果没有对话，显示提示信息
                                    if (conversationsData.length === 0) {
                                        noMsgElement.style.display = 'block';
                                        container.innerHTML = '';
                                        return;
                                    }
                                    
                                    // 否则隐藏提示，显示对话列表
                                    noMsgElement.style.display = 'none';
                                    
                                    // 更新列表内容
                                    container.innerHTML = conversationsData.map((conv, index) => {
                                        // 提取用户消息中的时间戳和实际内容
                                        let userTimePrefix = '';
                                        let userContent = conv.user || '';
                                        
                                        // 尝试匹配时间戳模式 [YYYY-MM-DD HH:MM:SS]
                                        const timeRegex = /^\s*\[(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\]\s*/;
                                        const match = userContent.match(timeRegex);
                                        if (match) {
                                            userTimePrefix = match[0];
                                            userContent = userContent.substring(match[0].length);
                                        }
                                        
                                        return `
                                        <div class="card mb-3 conversation-card" data-index="${index}">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <small class="text-muted">${conv.timestamp || '未知时间'}</small>
                                                <div>
                                                    <button type="button" class="btn btn-sm btn-outline-danger delete-btn" data-index="${index}">
                                                        <i class="bi bi-trash"></i> 删除
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                <div>
                                                    <div class="mb-3">
                                                        <label class="form-label fw-bold">用户消息</label>
                                                        ${userTimePrefix ? `<div class="text-muted mb-1 small">${userTimePrefix}</div>` : ''}
                                                        <textarea class="form-control user-message-input" rows="2" data-index="${index}" data-time-prefix="${userTimePrefix.replace(/"/g, '&quot;')}">${userContent}</textarea>
                                                    </div>
                                                    <div class="mb-2">
                                                        <label class="form-label fw-bold">系统回复</label>
                                                        <textarea class="form-control bot-message-input" rows="2" data-index="${index}">${conv.bot || ''}</textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    `}).join('');
                                    
                                    // 绑定文本区域的自动保存事件
                                    document.querySelectorAll('.user-message-input').forEach(textarea => {
                                        textarea.addEventListener('change', function() {
                                            const index = parseInt(this.getAttribute('data-index'));
                                            const timePrefix = this.getAttribute('data-time-prefix') || '';
                                            // 保存时重新组合时间戳前缀和用户编辑的内容
                                            conversationsData[index].user = timePrefix + this.value;
                                        });
                                        
                                        // 自动调整高度
                                        textarea.addEventListener('input', function() {
                                            this.style.height = 'auto';
                                            this.style.height = (this.scrollHeight) + 'px';
                                        });
                                        
                                        // 初始调整高度
                                        textarea.style.height = 'auto';
                                        textarea.style.height = (textarea.scrollHeight) + 'px';
                                    });
                                    
                                    document.querySelectorAll('.bot-message-input').forEach(textarea => {
                                        textarea.addEventListener('change', function() {
                                            const index = parseInt(this.getAttribute('data-index'));
                                            conversationsData[index].bot = this.value;
                                        });
                                        
                                        // 自动调整高度
                                        textarea.addEventListener('input', function() {
                                            this.style.height = 'auto';
                                            this.style.height = (this.scrollHeight) + 'px';
                                        });
                                        
                                        // 初始调整高度
                                        textarea.style.height = 'auto';
                                        textarea.style.height = (textarea.scrollHeight) + 'px';
                                    });
                                    
                                    // 删除按钮事件
                                    document.querySelectorAll('.delete-btn').forEach(btn => {
                                        btn.addEventListener('click', function() {
                                            const index = parseInt(this.getAttribute('data-index'));
                                            if (confirm('确定要删除这条对话吗？')) {
                                                conversationsData.splice(index, 1);
                                                updateConversationsList();
                                            }
                                        });
                                    });
                                }
                                
                                // 清空所有对话
                                document.getElementById('clearAllConversationsBtn').addEventListener('click', function() {
                                    if (confirm('确定要清空所有对话吗？此操作无法撤销！')) {
                                        conversationsData = [];
                                        updateConversationsList();
                                    }
                                });
                                
                                // 保存所有修改
                                document.getElementById('saveShortMemoryBtn').addEventListener('click', function() {
                                        // 保存短期记忆
                                        fetch('/save_short_memory', {
                                            method: 'POST',
                                            headers: {
                                                'Content-Type': 'application/json',
                                            },
                                            body: JSON.stringify({
                                                avatar: currentAvatar,
                                            user_id: currentUser,
                                            conversations: conversationsData
                                            })
                                        })
                                        .then(response => response.json())
                                        .then(data => {
                                            if (data.status === 'success') {
                                                showToast('短期记忆保存成功', 'success');
                                                modal.hide();
                                                
                                                // 移除模态框
                                                document.getElementById('shortMemoryModal').remove();
                                            } else {
                                                showToast('保存失败: ' + data.message, 'error');
                                            }
                                        })
                                        .catch(error => {
                                            showToast('保存失败: ' + error, 'error');
                                        });
                                });
                                
                                // 模态框关闭事件 - 清理DOM
                                document.getElementById('shortMemoryModal').addEventListener('hidden.bs.modal', function() {
                                    this.remove();
                                });
                                
                                // 初始化显示对话列表
                                updateConversationsList();
                                
                            } else {
                                showToast('加载短期记忆失败: ' + data.message, 'error');
                            }
                        })
                        .catch(error => {
                            showToast('加载短期记忆失败: ' + error, 'error');
                        });
                });
                
                selector.appendChild(viewRawBtn);
                selector.appendChild(editCoreMemoryBtn);
                selector.appendChild(editShortMemoryBtn);
            }
            
            // 保存角色设定
            function saveAvatarSettings() {
                const avatarData = {
                    avatar: currentAvatar, // 添加当前人设名称
                    task: document.getElementById('task').value,
                    role: document.getElementById('role').value,
                    appearance: document.getElementById('appearance').value,
                    experience: document.getElementById('experience').value,
                    personality: document.getElementById('personality').value,
                    classic_lines: document.getElementById('classic_lines').value,
                    preferences: document.getElementById('preferences').value,
                    notes: document.getElementById('notes').value
                };

                console.log('准备发送的数据:', avatarData);  // 调试信息

                fetch('/save_avatar', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(avatarData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        showToast('角色设定已成功保存', 'success');
                        // 重新加载内容以获取最新的原始MD
                        loadAvatarContent(currentAvatar);
                    } else {
                        showToast('保存失败: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    showToast('保存失败: ' + error, 'error');
                });
            }
            
            // 创建新人设
            function createNewAvatar() {
                const avatarName = document.getElementById('newAvatarName').value.trim();
                
                if (!avatarName) {
                    showToast('请输入人设名称', 'error');
                    return;
                }

                if (!/^[a-zA-Z0-9_]+$/.test(avatarName)) {
                    showToast('人设名称只能包含英文字母、数字和下划线', 'error');
                    return;
                }

                fetch('/create_avatar', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ avatar_name: avatarName })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        showToast('人设创建成功', 'success');
                        // 关闭模态框
                        bootstrap.Modal.getInstance(document.getElementById('newAvatarModal')).hide();
                        // 重新加载人设列表
                        loadAvatarList();
                        // 清空输入框
                        document.getElementById('newAvatarName').value = '';
                    } else {
                        showToast('创建失败: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    showToast('创建失败: ' + error, 'error');
                });
            }
            
            // 删除当前人设
            function deleteCurrentAvatar() {
                const avatarName = document.getElementById('avatarSelector').value;
                
                fetch('/delete_avatar', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ avatar_name: avatarName })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        showToast('人设已删除', 'success');
                        // 关闭模态框
                        bootstrap.Modal.getInstance(document.getElementById('deleteAvatarModal')).hide();
                        // 重新加载人设列表
                        loadAvatarList();
                    } else {
                        showToast('删除失败: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    showToast('删除失败: ' + error, 'error');
                });
            }
            
            // 初始化各种事件监听器
            function initEventListeners() {
                // 人设选择器变更事件
                document.getElementById('avatarSelector').addEventListener('change', function() {
                    const selectedAvatar = this.value;
                    currentAvatar = selectedAvatar;
                    loadAvatarContent(selectedAvatar);
                });
                
                // 用户选择器变更事件
                document.getElementById('userSelector').addEventListener('change', function() {
                    const selectedUser = this.value;
                    currentUser = selectedUser;
                    localStorage.setItem(`lastSelectedUser_${currentAvatar}`, selectedUser);
                    // 这里不需要重新加载角色内容，只需记录当前选中的用户
                    showToast(`已切换到用户: ${selectedUser}`, 'success');
                });
                
                // 刷新用户列表按钮
                document.getElementById('refreshUsersBtn').addEventListener('click', function() {
                    loadUserList(currentAvatar);
                    showToast('用户列表已刷新', 'success');
                });
                
                // 保存按钮点击事件
                document.getElementById('saveButton').addEventListener('click', saveAvatarSettings);
                
                // 新建人设按钮点击事件
                document.getElementById('newAvatarBtn').addEventListener('click', function() {
                    new bootstrap.Modal(document.getElementById('newAvatarModal')).show();
                });
                
                // 删除人设按钮点击事件
                document.getElementById('deleteAvatarBtn').addEventListener('click', function() {
                    new bootstrap.Modal(document.getElementById('deleteAvatarModal')).show();
                });
                
                // 创建人设按钮点击事件
                document.getElementById('createAvatarBtn').addEventListener('click', createNewAvatar);
                
                // 确认删除人设按钮点击事件
                document.getElementById('confirmDeleteBtn').addEventListener('click', deleteCurrentAvatar);

            // 获取人设按钮点击事件
            document.getElementById('getAvatarBtn').addEventListener('click', function() {
                    new bootstrap.Modal(document.getElementById('getAvatarModal')).show();
                });
                
                // 自动调整文本框高度
                const textareas = document.querySelectorAll('textarea');
                textareas.forEach(textarea => {
                    textarea.addEventListener('input', function() {
                        this.style.height = 'auto';
                        this.style.height = (this.scrollHeight) + 'px';
                    });
                });
            }
            
            // 添加工具栏按钮
            function addToolbarButtons() {
                // 添加原始MD查看按钮
                addViewRawButton();
                
                // 在加载时初始化
                initEventListeners();
                
                // 加载人设列表
                loadAvatarList();
            }
            
            // 执行初始化
            addToolbarButtons();

            // 显示提示消息
            function showToast(message, type = 'success') {
                const toast = document.createElement('div');
                toast.className = 'toast-container position-fixed top-0 start-50 translate-middle-x p-3';
                toast.innerHTML = `
                    <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                        <div class="d-flex">
                            <div class="toast-body">
                                <i class="bi bi-${type === 'success' ? 'check-circle' : 'x-circle'} me-2"></i>${message}
                            </div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                    </div>
                `;
                document.body.appendChild(toast);
                const toastEl = toast.querySelector('.toast');
                const bsToast = new bootstrap.Toast(toastEl, {
                    delay: 3000
                });
                bsToast.show();
                
                // 自动移除 toast
                toastEl.addEventListener('hidden.bs.toast', function () {
                    toast.remove();
                });
            }
        });
    </script>
</body>
</html>