{"version": "1.4.1", "api_providers": [{"id": "kourichat-global", "name": "KouriChat API (推荐)", "url": "https://api.kourichat.com/v1", "register_url": "https://api.kourichat.com/register", "status": "active", "priority": 1}, {"id": "siliconflow", "name": "硅基流动 API", "url": "https://api.siliconflow.cn/v1/", "register_url": "https://www.siliconflow.cn", "status": "active", "priority": 2}, {"id": "deepseek", "name": "DeepSeek API", "url": "https://api.deepseek.com/v1", "register_url": "https://platform.deepseek.com", "status": "active", "priority": 3}, {"id": "ollama", "name": "本地 Ollama", "url": "http://localhost:11434/api/chat", "register_url": "https://ollama.ai", "status": "active", "priority": 4}], "models": {"kourichat-global": [{"id": "kourichat-v3", "name": "KouriChat V3"}, {"id": "kourichat-r1", "name": "KouriChat R1"}, {"id": "deepseek-v3", "name": "DeepSeek V3"}, {"id": "deepseek-r1", "name": "DeepSeek R1"}, {"id": "grok-3", "name": "Grok 3 (官方版本)"}, {"id": "gemini-2.5-pro", "name": "Gemini 2.5 Pro模型"}, {"id": "claude-3-5-sonnet-20241022", "name": "Claude 3.5 Sonnet (2024/10)"}, {"id": "gpt-4o", "name": "GPT-4o"}], "siliconflow": [{"id": "deepseek-ai/DeepSeek-V3", "name": "硅基格式V3模型（免费额度版）"}, {"id": "deepseek-ai/DeepSeek-R1", "name": "硅基格式R1模型（免费额度版）"}, {"id": "Pro/deepseek-ai/DeepSeek-V3", "name": "硅基格式V3模型（付费版）"}, {"id": "Pro/deepseek-ai/DeepSeek-R1", "name": "硅基格式R1模型（付费版）"}], "deepseek": [{"id": "deepseek-chat", "name": "deepseek官方V3模型"}, {"id": "deepseek-reasoner", "name": "deepseek官方R1模型"}]}, "vision_api_providers": [{"id": "kourichat-global", "name": "KouriChat API (推荐)", "url": "https://api.kourichat.com/v1", "register_url": "https://api.kourichat.com/register", "status": "active", "priority": 1}, {"id": "moonshot", "name": "Moonshot AI", "url": "https://api.moonshot.cn/v1", "register_url": "https://platform.moonshot.cn/console/api-keys", "status": "active", "priority": 2}, {"id": "openai", "name": "OpenAI", "url": "https://api.openai.com/v1", "register_url": "https://platform.openai.com/api-keys", "status": "active", "priority": 3}, {"id": "siliconflow", "name": "硅基流动 API", "url": "https://api.siliconflow.cn/v1/", "register_url": "https://www.siliconflow.cn", "status": "active", "priority": 4}], "vision_models": {"kourichat-global": [{"id": "kourichat-vision", "name": "KouriChat Vision (推荐)"}, {"id": "gemini-2.5-pro", "name": "Gemini 2.5 Pro"}, {"id": "gpt-4o", "name": "GPT-4o"}], "moonshot": [{"id": "moonshot-v1-8k-vision-preview", "name": "Moonshot V1 8K Vision (推荐)"}, {"id": "moonshot-v1-32k-vision", "name": "Moonshot V1 32K Vision"}]}}