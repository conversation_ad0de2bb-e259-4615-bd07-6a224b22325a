<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- SEO Meta Tags -->
    <title>KouriChat - AI 情感陪伴系统控制台</title>
    <meta name="description" content="KouriChat 是基于 DeepSeek LLM 的情感陪伴系统，支持微信机器人接入，提供沉浸式角色扮演和多轮对话体验。">
    <meta name="keywords" content="KouriChat, AI陪伴, DeepSeek, 情感陪伴, 角色扮演, AI助手, 微信机器人, LLM, AI对话">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://github.com/KouriChat/KouriChat">
    <meta property="og:title" content="KouriChat - AI 情感陪伴系统">
    <meta property="og:description" content="基于 DeepSeek LLM 的情感陪伴系统，支持微信机器人接入，提供沉浸式角色扮演体验。">
    <meta property="og:image" content="https://raw.githubusercontent.com/KouriChat/KouriChat/main/ATRI.jpg">
    
    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://github.com/KouriChat/KouriChat">
    <meta name="twitter:title" content="KouriChat - AI 情感陪伴系统">
    <meta name="twitter:description" content="基于 DeepSeek LLM 的情感陪伴系统，支持微信机器人接入，提供沉浸式角色扮演体验。">
    <meta name="twitter:image" content="https://raw.githubusercontent.com/KouriChat/KouriChat/main/ATRI.jpg">
    
    <!-- 其他 Meta -->
    <meta name="author" content="umaru-233">
    <meta name="robots" content="index, follow">
    <meta name="language" content="zh-CN">
    <meta name="revisit-after" content="7 days">
    <meta name="theme-color" content="#6366f1">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/static/mom.ico">
    
    <!-- 原有的样式表引用 -->
    <link href="/static/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdmirror.com/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" rel="stylesheet">
    
    <!-- Bootstrap JavaScript 库 -->
    <script src="/static/js/bootstrap.bundle.min.js"></script>
    
    <!-- 添加结构化数据 -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "KouriChat",
        "description": "基于 DeepSeek LLM 的情感陪伴系统，支持微信和机器人接入，提供沉浸式角色扮演体验。",
        "operatingSystem": "Windows",
        "applicationCategory": "ChatApplication",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "CNY"
        },
        "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.8",
            "ratingCount": "865",
            "reviewCount": "81"
        },
        "author": {
            "@type": "Person",
            "name": "KouriChat",
            "url": "https://github.com/KouriChat"
        },
        "downloadUrl": "https://github.com/KouriChat/KouriChat",
        "softwareVersion": "1.4.1",
        "keywords": "AI陪伴,DeepSeek,情感陪伴,角色扮演,AI助手,微信机器人,QQ机器人,LLM,AI对话"
    }
    </script>
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #4f46e5;
            --background-color: #f8fafc;
            --text-color: #1e293b;
            --card-bg: rgba(255, 255, 255, 0.8);
            --card-border: rgba(255, 255, 255, 0.5);
            --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        [data-bs-theme="dark"] {
            --primary-color: #818cf8;
            --secondary-color: #6366f1;
            --background-color: #0f172a;
            --text-color: #e2e8f0;
            --card-bg: rgba(30, 41, 59, 0.8);
            --card-border: rgba(255, 255, 255, 0.1);
            --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
        }

        body {
            background-color: var(--background-color);
            color: var(--text-color);
            transition: all 0.3s ease;
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            min-height: 100vh;
        }

        /* 毛玻璃效果卡片 */
        .glass-panel, .info-card ,.modal-content,.modal-header,.modal-footer,.modal-body{
            background: var(--card-bg);
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
            border: 1px solid var(--card-border);
            box-shadow: var(--card-shadow);
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        .glass-panel:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 12px -1px rgba(0, 0, 0, 0.15), 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }


        .info-card:hover {
            transform: translateY(-1px);
            box-shadow: var(--card-shadow);
        }

        /* 进度条样式 */
        .progress {
            height: 0.5rem;
            background-color: rgba(var(--bs-primary-rgb), 0.1);
            border-radius: 1rem;
            overflow: hidden;
        }

        /* 按钮样式 */
        .btn-glass {
            background: var(--card-bg);
            border: 1px solid var(--card-border);
            -webkit-backdrop-filter: blur(5px);
            backdrop-filter: blur(5px);
            transition: all 0.3s ease;
        }

        .btn-glass:hover {
            transform: translateY(-1px);
            box-shadow: var(--card-shadow);
        }

        /* 导航栏样式 */
        .navbar {
            background: var(--card-bg) !important;
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--card-border);
        }

        /* 徽章样式 */
        .badge {
            padding: 0.5em 0.8em;
            border-radius: 0.5rem;
        }

        /* 列表组样式 */
        .list-group-item {
            background: transparent;
            border-color: var(--card-border);
            transition: all 0.3s ease;
        }

        .list-group-item:hover {
            background: var(--card-bg);
            transform: translateX(4px);
        }

        /* Toast 样式 */
        .toast {
            background: var(--card-bg);
            border: 1px solid var(--card-border);
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
        }

        /* 链接样式 */
        a {
            transition: all 0.3s ease;
        }

        a:hover {
            text-decoration: none;
            opacity: 0.8;
        }

        /* 暗色模式切换按钮 */
        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .system-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .status-badge {
            position: relative;
            padding-left: 1.5rem;
        }

        .status-badge::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 0.75rem;
            height: 0.75rem;
            border-radius: 50%;
            background-color: currentColor;
        }

        .status-running::before {
            background-color: #10b981;
        }

        .status-stopped::before {
            background-color: #ef4444;
        }

        .chart-container {
            height: 200px;
            margin-top: 1rem;
        }

        /* 移除重复的导航栏护眼模式开关布局样式 */
        /*
        .navbar .form-check.form-switch {
            display: flex;
            align-items: center;
            height: 100%;
            margin: 0;
        }

        .navbar .form-check-label {
            display: flex;
            align-items: center;
            margin: 0 0 0 8px;
            line-height: 1;
        }

        .navbar .form-check-input {
            margin: 0;
            vertical-align: middle;
        }
        */

        #updateStatus, #startStatus {
            display: none;
            margin-top: 1rem;
        }

        .loading-spinner {
            width: 1rem;
            height: 1rem;
            margin-right: 0.5rem;
        }

        .log-container {
            height: 400px;
            background: var(--card-bg);
            border: 1px solid var(--card-border);
            border-radius: 0.5rem;
            padding: 1rem;
            overflow-y: auto;
            font-family: 'Consolas', monospace;
            font-size: 0.9rem;
            scroll-behavior: smooth;
        }

        .log-line {
            padding: 4px 8px;
            border-bottom: 1px solid var(--card-border);
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .log-timestamp {
            color: #f97316;
            font-weight: 500;
            margin-right: 8px;
        }

        .log-level-info {
            color: #3b82f6;
        }

        .log-level-success {
            color: #10b981;
        }

        .log-level-warning {
            color: #f59e0b;
        }

        .log-level-error {
            color: #ef4444;
        }

        .log-container::-webkit-scrollbar {
            width: 6px;
        }

        .log-container::-webkit-scrollbar-track {
            background: transparent;
        }

        .log-container::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 3px;
        }

        .log-container:hover::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.3);
        }

        /* 机器人控制按钮样式 */
        .bot-controls .btn {
            padding: 0.5rem 1.2rem;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            border: 1px solid var(--card-border);
        }

        /* 启动按钮样式 */
        .bot-controls .btn.text-success {
            background: rgba(25, 135, 84, 0.1);
        }
        
        .bot-controls .btn.text-success:hover {
            background: rgba(25, 135, 84, 0.2);
            color: #198754 !important;
        }

        /* 停止按钮样式 */
        .bot-controls .btn.text-danger {
            background: rgba(220, 53, 69, 0.1);
        }
        
        .bot-controls .btn.text-danger:hover {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545 !important;
        }

        /* 更新按钮样式 */
        .bot-controls .btn.text-primary {
            background: rgba(13, 110, 253, 0.1);
        }
        
        .bot-controls .btn.text-primary:hover {
            background: rgba(13, 110, 253, 0.2);
            color: #0d6efd !important;
        }

        .bot-controls .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--card-shadow);
        }

        .bot-controls .btn:active {
            transform: translateY(0);
        }

        /* 禁用状态样式 */
        .bot-controls .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* 加载状态指示器样式 */
        #botLoadingStatus {
            background: var(--card-bg);
            border: 1px solid var(--card-border);
            margin-bottom: 1rem;
        }

        /* 改进日志容器样式 */
        .log-container {
            height: 400px;
            background: var(--card-bg);
            border: 1px solid var(--card-border);
            border-radius: 0.5rem;
            padding: 1rem;
            overflow-y: auto;
            font-family: 'Consolas', monospace;
            font-size: 0.9rem;
            scroll-behavior: smooth;
        }

        /* 修改日志内容容器样式 */
        .logs {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
        }

        /* 控制台输入区域样式 */
        .console-input-area {
            background: var(--card-bg);
            border: 1px solid var(--card-border);
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .console-input-area .input-group {
            background: transparent;
        }

        .console-input-area .form-control {
            background: transparent;
            color: var(--text-color);
            font-family: 'Consolas', monospace;
        }

        .console-input-area .form-control:focus {
            box-shadow: none;
            background: transparent;
        }

        .console-input-area .input-group-text {
            color: var(--text-color);
        }

        /* 命令输出样式 */
        .command-input {
            color: #0d6efd;
            font-weight: 500;
        }

        .command-output {
            color: #198754;
        }

        .command-error {
            color: #dc3545;
        }

        .modal-content {
            border: 1px solid var(--card-border);
            box-shadow: var(--card-shadow);
        }
        
        .modal-header, .modal-footer {
            border-color: var(--card-border);
        }
        
        .modal-body {
            color: var(--text-color);
        }
        
        .btn-close {
            color: var(--text-color);
        }
        
        /* 动画效果 */
        .modal.fade .modal-dialog {
            transition: transform 0.2s ease-out;
        }
        
        .modal.fade .modal-content {
            transform: scale(0.95);
            transition: transform 0.2s ease-out;
        }
        
        .modal.show .modal-content {
            transform: scale(1);
        }

        /* 添加新的样式类 */
        .avatar-img {
            width: 64px;
            height: 64px;
        }

        .progress-bar-width-0 {
            width: 0%;
        }

        .modal-content-glass {
            background: var(--card-bg);
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
        }

        /* 更新通知样式 */
        .update-notification {
            animation: slideIn 0.5s ease-out forwards;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }
        
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        .update-notification:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 20px rgba(0, 0, 0, 0.2);
        }
        
        .update-notification .btn-close {
            font-size: 0.8rem;
            padding: 0.25rem;
        }
        
        .log-line:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }
        
        /* 公告模态框样式 */
        .announcement-body {
            max-height: 45vh;
            overflow-y: auto;
            padding: 1.5rem;
            line-height: 1.6;
        }
        
        #announcementContent {
            font-size: 1.05rem;
        }
        
        #announcementContent strong {
            color: var(--primary-color);
        }
        
        #announcementContent .border-top {
            border-top: 1px solid rgba(0,0,0,0.1) !important;
        }
        
        [data-bs-theme="dark"] #announcementContent .border-top {
            border-top: 1px solid rgba(255,255,255,0.1) !important;
        }

        .spin {
            animation: spin 1s linear infinite;
        }
        
        /* 微信重连按钮样式 */
        .btn-wechat {
            background-color: #07C160;  /* 微信绿色 */
            color: white;
            border: none;
            padding: 0.5rem 1.2rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-size: 0.95rem;
            height: 38px;
            line-height: 1;
        }

        .btn-wechat:hover {
            background-color: #06ad56;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 2px 4px rgba(7, 193, 96, 0.3);
        }

        .btn-wechat:active {
            transform: translateY(0);
            box-shadow: none;
        }

        .btn-wechat .bi-wechat {
            font-size: 1.1em;
            margin-right: 4px;
        }
        
        .btn-wechat:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
    </style>
    <!-- 添加 dark-mode.js -->
    <script src="/static/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/dark-mode.js"></script>
</head>
<body data-show-announcement="{{ show_announcement|tojson }}">
    <!-- 替换原有的导航栏部分 -->
    {% with active_page = 'dashboard' %}
    {% include 'navbar.html' %}
    {% endwith %}

    <div class="container py-4">
        <div class="glass-panel mb-4">
            <div class="row align-items-center g-4">
                <div class="col-md-8">
                    <div class="d-flex align-items-center">
                        <img src="https://git.kourichat.com/assets/img/favicon.png" 
                             alt="umaru-233" 
                             class="rounded-circle me-3 avatar-img"
                             width="64"
                             height="64">
                        <div>
                            <h4 class="mb-1">KouriChat</h4>
                            <p class="text-muted mb-2">
                                <small>by <a href="https://github.com/KouriChat" target="_blank" rel="noopener" class="text-decoration-none">KouriChat Team</a></small>
                            </p>
                            <div class="d-flex gap-3">
                                <a href="https://github.com/KouriChat/KouriChat" target="_blank" 
                                   class="text-decoration-none text-muted" rel="noopener">
                                    <i class="bi bi-github me-1"></i>GitHub
                                </a>
                                <span class="text-muted">
                                    <i class="bi bi-star-fill me-1 text-warning"></i><span id="githubStars">-</span>
                                </span>
                                <span class="text-muted">
                                    <i class="bi bi-git me-1"></i><span id="githubForks">-</span>
                                </span>
                                <span class="text-muted">
                                    <i class="bi bi-exclamation-circle me-1"></i><span id="githubIssues">-</span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-grid gap-2">

                        <a href="https://github.com/KouriChat/KouriChat" target="_blank" 
                           class="btn btn-primary" rel="noopener">
                            <i class="bi bi-github me-2"></i>访问项目
                        </a>
                        <a href="https://github.com/KouriChat/KouriChat/fork" target="_blank" 
                           class="btn btn-outline-primary" rel="noopener">
                            <i class="bi bi-git me-2"></i>Fork 项目
                        </a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row g-4">
                <div class="col-md-8">
                    <h5 class="mb-3">项目介绍</h5>
                    <p class="mb-3">
                        KouriChat 是一个基于大语言模型的情感陪伴系统，支持微信机器人接入，提供沉浸式角色扮演和多轮对话体验。
                        系统采用 DeepSeek 等先进的 LLM 模型，通过精心设计的提示词和上下文管理，
                        让 AI 能够模拟更加自然、真实的情感互动。推荐使用 Kourichat V3 模型以获得最佳体验。
                    </p>
                    <p class="mb-3">
                        <strong>主要特性：</strong>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-check-circle-fill text-success me-2"></i>支持微信机器人接入</li>
                            <li><i class="bi bi-check-circle-fill text-success me-2"></i>提供多种 AI 模型选择</li>
                            <li><i class="bi bi-check-circle-fill text-success me-2"></i>支持图片识别和生成</li>
                            <li><i class="bi bi-check-circle-fill text-success me-2"></i>自定义角色和人设</li>
                            <li><i class="bi bi-check-circle-fill text-success me-2"></i>情感化对话和记忆系统</li>
                        </ul>
                    </p>
                    <div class="d-flex flex-wrap gap-2">
                        <span class="badge bg-primary">WeChat</span>
                        <span class="badge bg-success">QQBot</span>
                        <span class="badge bg-info">LLM</span>
                        <span class="badge bg-warning">AI 情感陪伴</span>
                        <span class="badge bg-secondary">Python</span>
                        <span class="badge bg-danger">DeepSeek</span>
                        <span class="badge bg-info">角色扮演</span>
                        <span class="badge bg-primary">多轮对话</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <h5 class="mb-3">快速链接</h5>
                    <div class="list-group">
                        <a href="https://github.com/KouriChat/KouriChat/issues" target="_blank" 
                           class="list-group-item list-group-item-action" rel="noopener">
                            <i class="bi bi-exclamation-circle me-2"></i>问题反馈
                        </a>
                        <a href="https://kourichat.com/" target="_blank" 
                           class="list-group-item list-group-item-action" rel="noopener">
                            <i class="bi bi-book me-2"></i>官方文档
                        </a>
                        <a href="https://api.kourichat.com/" target="_blank" 
                           class="list-group-item list-group-item-action" rel="noopener">
                            <i class="bi bi-cloud me-2"></i>API 服务
                        </a>
                        <a href="https://github.com/KouriChat/KouriChat/pulls" target="_blank" 
                           class="list-group-item list-group-item-action" rel="noopener">
                            <i class="bi bi-git me-2"></i>提交PR
                        </a>
                        <a href="https://github.com/KouriChat/KouriChat/releases" target="_blank" 
                           class="list-group-item list-group-item-action" rel="noopener">
                            <i class="bi bi-download me-2"></i>下载发布版
                        </a>
                        <button class="list-group-item list-group-item-action" type="button" onclick="showManualAnnouncement()">
                            <i class="bi bi-bell me-2"></i>查看公告
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="glass-panel">
            <h4 class="mb-4">
                <i class="bi bi-pc-display me-2"></i>系统状态
            </h4>
            <div class="system-info">
                <div class="info-card">
                    <h6 class="d-flex justify-content-between">
                        <span><i class="bi bi-cpu me-2"></i>CPU</span>
                        <span id="cpuUsage">0%</span>
                    </h6>
                    <div class="progress">
                        <div id="cpuProgress" class="progress-bar bg-primary progress-bar-width-0"></div>
                    </div>
                </div>
                <div class="info-card">
                    <h6 class="d-flex justify-content-between">
                        <span><i class="bi bi-memory me-2"></i>内存</span>
                        <span id="memoryUsage">0/0 GB</span>
                    </h6>
                    <div class="progress">
                        <div id="memoryProgress" class="progress-bar bg-success progress-bar-width-0"></div>
                    </div>
                </div>
                <div class="info-card">
                    <h6 class="d-flex justify-content-between">
                        <span><i class="bi bi-hdd me-2"></i>磁盘</span>
                        <span id="diskUsage">0/0 GB</span>
                    </h6>
                    <div class="progress">
                        <div id="diskProgress" class="progress-bar bg-info progress-bar-width-0"></div>
                    </div>
                </div>
                <div class="info-card">
                    <h6 class="d-flex justify-content-between">
                        <span><i class="bi bi-wifi me-2"></i>网络</span>
                    </h6>
                    <div class="d-flex justify-content-between align-items-center mt-2">
                        <div>
                            <small class="text-muted d-block">上传</small>
                            <span id="uploadSpeed" class="text-success">0 KB/s</span>
                        </div>
                        <div>
                            <small class="text-muted d-block">下载</small>
                            <span id="downloadSpeed" class="text-danger">0 KB/s</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="glass-panel">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">
                        <i class="bi bi-boxes me-2"></i>依赖管理
                    </h4>
                    <div>
                        <button class="btn btn-glass text-info me-2" onclick="checkDependencies()">
                            <i class="bi bi-search me-1"></i>检查依赖
                        </button>
                        <button class="btn btn-glass text-primary me-2" onclick="checkAndInstallDependencies()">
                            <i class="bi bi-download me-1"></i>安装依赖
                        </button>
                        <button class="btn btn-glass text-success" onclick="checkAndInstallDependencies()">
                            <i class="bi bi-arrow-repeat me-1"></i>更新依赖
                        </button>
                    </div>
                </div>
                
                <div id="dependencyStatus" class="alert d-none">
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-2" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <span id="dependencyStatusText">正在检查依赖...</span>
                    </div>
                </div>
                
                <div class="info-card">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">Python版本</span>
                        <span id="pythonVersion" class="badge bg-primary">
                            <i class="bi bi-question-circle me-1"></i>检查中...
                        </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">pip状态</span>
                        <span id="pipStatus" class="badge bg-secondary">
                            <i class="bi bi-question-circle me-1"></i>检查中...
                        </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">依赖状态</span>
                        <span id="depsStatus" class="badge bg-secondary">
                            <i class="bi bi-question-circle me-1"></i>检查中...
                        </span>
                    </div>
                </div>
                
                <div id="missingDeps" class="mt-3 d-none">
                    <small class="text-muted">缺失的依赖项：</small>
                    <div class="missing-deps-list mt-2"></div>
                </div>
            </div>

            <div class="glass-panel">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">
                        <i class="bi bi-robot me-2"></i>机器人状态
                    </h4>
                    <div class="bot-controls">
                        <button class="btn btn-wechat me-2" onclick="reconnectWechat()" id="reconnectBtn">
                            <i class="bi bi-wechat"></i>微信掉线重连
                        </button>
                        <button class="btn btn-glass text-success me-2" onclick="startBot()" id="startBotBtn">
                            <i class="bi bi-play-fill me-1"></i>启动
                        </button>
                        <button class="btn btn-glass text-danger me-2" onclick="stopBot()" id="stopBotBtn" disabled>
                            <i class="bi bi-stop-fill me-1"></i>停止
                        </button>
                        <button class="btn btn-glass text-primary" onclick="checkUpdate()">
                            <i class="bi bi-cloud-arrow-up me-1"></i>更新
                        </button>
                    </div>
                </div>
                
                <!-- 添加加载状态指示器 -->
                <div id="botLoadingStatus" class="alert alert-info d-none">
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-2" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <span id="botLoadingText">正在启动机器人...</span>
                    </div>
                </div>

                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <h6 class="status-badge" id="botStatus">
                            <span class="status-stopped">已停止</span>
                        </h6>
                        <small class="text-muted" id="botUptime">运行时间: 0分钟</small>
                    </div>
                </div>
                
                <!-- 日志显示区域 -->
                <div class="bot-logs mt-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">控制台</h6>
                        <div>
                            <button class="btn btn-glass me-2" onclick="scrollToBottom()">
                                <i class="bi bi-arrow-down me-1"></i>滚动到底部
                            </button>
                            <button class="btn btn-glass" onclick="clearLogs()">
                                <i class="bi bi-trash me-1"></i>清空日志
                            </button>
                        </div>
                    </div>
                    <div id="logContainer" class="log-container">
                        <div class="logs"></div>
                    </div>
                    <!-- 添加控制台输入区域 -->
                    <div class="console-input-area mt-3">
                        <div class="input-group">
                            <span class="input-group-text bg-transparent border-0">
                                <i class="bi bi-terminal"></i>
                            </span>
                            <input type="text" 
                                   class="form-control border-0" 
                                   id="consoleInput" 
                                   placeholder="输入指令并按Enter发送..."
                                   onkeydown="handleConsoleInput(event)">
                        </div>
                    </div>
                </div>
            </div>

            <!-- <div class="glass-panel">
                <h4 class="mb-4">
                    <i class="bi bi-person-circle me-2"></i>用户信息
                    <button class="btn btn-sm btn-outline-primary float-end" onclick="updateUserInfo()">
                        <i class="bi bi-arrow-clockwise me-1"></i>刷新
                    </button>
                </h4>
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="info-card">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted">账户状态</span>
                                <span id="userStatus" class="badge rounded-pill">获取中...</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-muted">用户名</span>
                                <span id="userName" class="text-primary">获取中...</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-card">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted">邮箱</span>
                                <span id="userEmail" class="text-secondary">获取中...</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-muted">账户类型</span>
                                <span id="userRole" class="badge bg-info">普通用户</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="info-card">
                            <h6 class="mb-3">账户余额</h6>
                            <div class="row g-2">
                                <div class="col-md-4">
                                    <div class="p-3 rounded bg-primary bg-opacity-10">
                                        <div class="text-primary mb-1">当前余额</div>
                                        <div id="userBalance" class="h5 mb-0">获取中...</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="p-3 rounded bg-success bg-opacity-10">
                                        <div class="text-success mb-1">充值金额</div>
                                        <div id="chargeBalance" class="h5 mb-0">获取中...</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="p-3 rounded bg-info bg-opacity-10">
                                        <div class="text-info mb-1">累计总额</div>
                                        <div id="totalBalance" class="h5 mb-0">获取中...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
        </div>
    </div>

    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="toast" class="toast align-items-center" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body"></div>
                <button type="button" class="btn-close me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    </div>

    <footer class="glass-panel mt-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-md-6">
                    <h5 class="mb-3">关于 KouriChat</h5>
                    <p class="mb-3">
                        KouriChat 是一个基于 DeepSeek LLM 的情感陪伴程序，支持微信机器人接入。
                        项目提供沉浸式角色扮演和多轮对话支持，让 AI 陪伴更加真实自然。
                    </p>
                    <div class="d-flex gap-3">
                        <a href="https://github.com/KouriChat/KouriChat" target="_blank" 
                           class="text-decoration-none text-muted" rel="noopener">
                            <i class="bi bi-github"></i> GitHub
                        </a>
                        <a href="https://github.com/KouriChat/KouriChat/wiki" target="_blank" 
                           class="text-decoration-none text-muted" rel="noopener">
                            <i class="bi bi-book"></i> 文档
                        </a>
                        <a href="https://github.com/KouriChat/KouriChat/issues" target="_blank" 
                           class="text-decoration-none text-muted" rel="noopener">
                            <i class="bi bi-bug"></i> 反馈
                        </a>
                    </div>
                </div>
                <div class="col-md-3">
                    <h5 class="mb-3">特色功能</h5>
                    <ul class="list-unstyled">
                        <li><i class="bi bi-check2-circle me-2"></i>微信机器人接入</li>
                        <li><i class="bi bi-check2-circle me-2"></i>DeepSeek LLM 支持</li>
                        <li><i class="bi bi-check2-circle me-2"></i>角色扮演系统</li>
                        <li><i class="bi bi-check2-circle me-2"></i>多轮对话支持</li>
                        <li><i class="bi bi-check2-circle me-2"></i>情感表情系统</li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5 class="mb-3">技术支持</h5>
                    <ul class="list-unstyled">
                        <li><i class="bi bi-envelope me-2"></i><EMAIL></li>
                        <li><i class="bi bi-people me-2"></i>QQ群：715616260</li>
                        <li><i class="bi bi-person me-2"></i>作者：umaru-233</li>
                        <li><i class="bi bi-star me-2"></i>Stars：<span id="footerGithubStars">-</span></li>
                        <li><i class="bi bi-git me-2"></i>Forks：<span id="footerGithubForks">-</span></li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center text-muted">
                <small>
                    © 2024 KouriChat. Made with <i class="bi bi-heart-fill text-danger"></i> by 
                    <a href="https://github.com/umaru-233" target="_blank" rel="noopener" class="text-decoration-none">umaru-233</a>
                </small>
            </div>
        </div>
    </footer>

    <div class="modal fade" id="stopBotModal" tabindex="-1" aria-labelledby="stopBotModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content modal-content-glass">
                <div class="modal-header border-bottom-0">
                    <h5 class="modal-title" id="stopBotModalLabel">
                        <i class="bi bi-exclamation-triangle-fill text-warning me-2"></i>停止确认
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>确定要停止机器人吗？这将中断所有正在进行的对话。</p>
                </div>
                <div class="modal-footer border-top-0">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="confirmStopBot()">
                        <i class="bi bi-stop-fill me-1"></i>确认停止
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 公告提示模态框 -->
    <div class="modal fade" id="announcementModal" tabindex="-1" aria-labelledby="announcementModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="announcementModalLabel"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body announcement-body">
                    <div id="announcementContent"></div>
                </div>
                <div class="modal-footer justify-content-between">
                    <small class="text-muted">注意：每次程序启动后，控制面板会自动获取更新情况，请用户留意</small>
                    <div>
                        <button type="button" class="btn btn-outline-secondary me-2" id="announcementDismissBtn">不再显示</button>
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal" id="announcementOkBtn">我知道了</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 更新通知模态框 -->
    <div class="modal fade" id="updateNotificationModal" tabindex="-1" aria-labelledby="updateNotificationModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="updateNotificationModalLabel">
                        <i class="bi bi-cloud-arrow-up me-2"></i>发现新版本
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-card mb-3">
                                <h6 class="text-muted mb-2">当前版本</h6>
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-tag me-2 text-secondary"></i>
                                    <span id="currentVersionText" class="fw-bold">-</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-card mb-3">
                                <h6 class="text-muted mb-2">最新版本</h6>
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-star me-2 text-warning"></i>
                                    <span id="latestVersionText" class="fw-bold text-primary">-</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="info-card mb-3">
                        <h6 class="text-muted mb-2">更新说明</h6>
                        <div id="updateDescription" class="text-break">
                            正在获取更新信息...
                        </div>
                    </div>

                    <div class="info-card">
                        <h6 class="text-muted mb-2">发布时间</h6>
                        <div id="updateTime" class="text-muted">
                            <i class="bi bi-calendar me-1"></i>-
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" id="updateLaterBtn" data-bs-dismiss="modal">
                        <i class="bi bi-clock me-1"></i>稍后更新
                    </button>
                    <button type="button" class="btn btn-success" id="updateNowBtn">
                        <i class="bi bi-download me-1"></i>立即更新
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 读取 data 属性来判断是否显示公告
        const shouldShowAnnouncement = document.body.dataset.showAnnouncement === 'true';

        // 通用的公告"不再显示"按钮事件处理函数
        function bindAnnouncementDismissHandler(announcementData) {
            const dismissBtn = document.getElementById('announcementDismissBtn');
            if (dismissBtn) {
                // 移除之前的事件监听器
                const newDismissBtn = dismissBtn.cloneNode(true);
                dismissBtn.parentNode.replaceChild(newDismissBtn, dismissBtn);
                
                // 绑定新的事件监听器
                newDismissBtn.addEventListener('click', function() {
                    // 获取当前公告ID
                    const announcementId = announcementData ? announcementData.id : null;
                    
                    // 调用API忽略公告
                    fetch('/dismiss_announcement', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            announcement_id: announcementId
                        })
                    })
                    .then(response => response.json())
                    .then(result => {
                        if (result.success) {
                            showToast('此公告将不再显示', 'success');
                            // 关闭模态框
                            const announcementModal = bootstrap.Modal.getInstance(document.getElementById('announcementModal'));
                            if (announcementModal) {
                                announcementModal.hide();
                            }
                        } else {
                            showToast('操作失败: ' + result.message, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('忽略公告失败:', error);
                        showToast('操作失败，请稍后重试', 'error');
                    });
                });
            }
        }

        // 添加 showToast 函数
        function showToast(message, type = 'info') {
            const toast = document.getElementById('toast');
            const toastBody = toast.querySelector('.toast-body');
            
            // 移除所有已有的背景色类
            toast.classList.remove('bg-success', 'bg-danger', 'bg-info', 'bg-warning');
            
            // 根据类型添加对应的背景色
            switch(type) {
                case 'success':
                    toast.classList.add('bg-success', 'text-white');
                    break;
                case 'error':
                    toast.classList.add('bg-danger', 'text-white');
                    break;
                case 'warning':
                    toast.classList.add('bg-warning');
                    break;
                default:
                    toast.classList.add('bg-info', 'text-white');
            }
            
            toastBody.textContent = message;
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }

        // 修改系统信息更新函数
        function updateSystemInfo() {
            fetch('/system_info')
                .then(response => response.json())
                .then(data => {
                    try {
                        // 更新其他系统信息
                        document.getElementById('cpuUsage').textContent = `${data.cpu}%`;
                        document.getElementById('cpuProgress').style.width = `${data.cpu}%`;
                        
                        document.getElementById('memoryUsage').textContent = 
                            `${data.memory.used}/${data.memory.total} GB`;
                        document.getElementById('memoryProgress').style.width = 
                            `${(data.memory.used/data.memory.total)*100}%`;
                        
                        document.getElementById('diskUsage').textContent = 
                            `${data.disk.used}/${data.disk.total} GB`;
                        document.getElementById('diskProgress').style.width = 
                            `${(data.disk.used/data.disk.total)*100}%`;
                        
                        // 格式化网络速度显示
                        const formatSpeed = (speed) => {
                            if (speed >= 1024) {
                                return `${(speed/1024).toFixed(2)} MB/s`;
                            }
                            return `${speed.toFixed(2)} KB/s`;
                        };
                        
                        document.getElementById('uploadSpeed').textContent = 
                            formatSpeed(data.network.upload);
                        document.getElementById('downloadSpeed').textContent = 
                            formatSpeed(data.network.download);
                        
                    } catch (error) {
                        console.error('更新系统信息失败:', error);
                    }
                })
                .catch(error => {
                    console.error('获取系统信息失败:', error);
                });
        }

        let isPollingLogs = false;
        
        // 修改启动机器人函数
        function startBot() {
            const loadingStatus = document.getElementById('botLoadingStatus');
            const loadingText = document.getElementById('botLoadingText');
            const startBtn = document.getElementById('startBotBtn');
            const stopBtn = document.getElementById('stopBotBtn');
            
            loadingStatus.classList.remove('d-none');
            loadingText.textContent = '正在启动机器人...';
            startBtn.disabled = true;
            
            fetch('/start_bot')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        document.getElementById('botStatus').innerHTML = 
                            '<span class="status-running">运行中</span>';
                        showToast('机器人启动成功', 'success');
                        startPollingLogs();
                        stopBtn.disabled = false;
                        startBtn.disabled = true;
                    } else {
                        showToast('机器人启动失败: ' + data.message, 'error');
                        startBtn.disabled = false;
                        stopBtn.disabled = true;
                    }
                    loadingStatus.classList.add('d-none');
                })
                .catch(error => {
                    showToast('启动失败: ' + error, 'error');
                    startBtn.disabled = false;
                    stopBtn.disabled = true;
                    loadingStatus.classList.add('d-none');
                });
        }

        // 添加日志轮询函数
        function startPollingLogs() {
            if (!isPollingLogs) {
                isPollingLogs = true;
                pollLogs();
            }
        }

        function formatLogLine(log) {
            // 解析日志格式
            const timestampMatch = log.match(/\[([\d:]+)\]/);
            if (!timestampMatch) return log;

            const timestamp = timestampMatch[1];
            const content = log.replace(/\[[\d:]+\]\s*/, '');

            // 确定日志级别
            let levelClass = 'log-level-info';
            if (content.includes('成功') || content.includes('完成')) {
                levelClass = 'log-level-success';
            } else if (content.includes('警告') || content.includes('warning')) {
                levelClass = 'log-level-warning';
            } else if (content.includes('错误') || content.includes('error') || content.includes('失败')) {
                levelClass = 'log-level-error';
            }

            // 返回格式化的HTML
            return `<span class="log-timestamp">[${timestamp}]</span><span class="${levelClass}">${content}</span>`;
        }

        function pollLogs() {
            fetch('/get_bot_logs')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        const logContainer = document.querySelector('.log-container');
                        const logsElement = document.querySelector('.logs');
                        const startBtn = document.getElementById('startBotBtn');
                        const stopBtn = document.getElementById('stopBotBtn');
                        
                        // 添加新日志
                        if (data.logs && data.logs.length > 0) {
                            // 检查是否在底部
                            const isAtBottom = logContainer.scrollHeight - logContainer.clientHeight <= logContainer.scrollTop + 1;
                            
                            // 将新日志保存到localStorage中
                            const savedLogs = JSON.parse(localStorage.getItem('botLogs') || '[]');
                            
                            data.logs.forEach(log => {
                                // 添加到savedLogs
                                savedLogs.push(log);
                                // 限制日志存储数量，避免localStorage过大
                                if (savedLogs.length > 1000) {
                                    savedLogs.shift();
                                }
                                
                                const logLine = document.createElement('div');
                                logLine.className = 'log-line';
                                logLine.innerHTML = formatLogLine(log);
                                logsElement.appendChild(logLine);
                            });
                            
                            // 更新localStorage
                            localStorage.setItem('botLogs', JSON.stringify(savedLogs));
                            
                            // 如果之前在底部，则滚动到新的底部
                            if (isAtBottom) {
                                logContainer.scrollTo({
                                    top: logContainer.scrollHeight,
                                    behavior: 'smooth'
                                });
                            }
                        }
                        
                        // 更新运行时间
                        if (data.uptime) {
                            document.getElementById('botUptime').textContent = 
                                '运行时间: ' + data.uptime;
                        }
                        
                        // 更新状态和按钮
                        if (data.is_running) {
                            document.getElementById('botStatus').innerHTML = 
                                '<span class="status-running">运行中</span>';
                            startBtn.disabled = true;   // 禁用启动按钮
                            stopBtn.disabled = false;   // 启用停止按钮
                        } else {
                            document.getElementById('botStatus').innerHTML = 
                                '<span class="status-stopped">已停止</span>';
                            startBtn.disabled = false;  // 启用启动按钮
                            stopBtn.disabled = true;    // 禁用停止按钮
                            isPollingLogs = false;
                            return;
                        }
                        
                        // 继续轮询
                        if (isPollingLogs) {
                            setTimeout(pollLogs, 1000);
                        }
                    }
                })
                .catch(error => {
                    console.error('获取日志失败:', error);
                    isPollingLogs = false;
                    // 发生错误时重置按钮状态
                    document.getElementById('startBotBtn').disabled = false;
                    document.getElementById('stopBotBtn').disabled = true;
                });
        }

        // 修改滚动到底部函数
        function scrollToBottom() {
            const logContainer = document.querySelector('.log-container');
            if (logContainer) {
                logContainer.scrollTo({
                    top: logContainer.scrollHeight,
                    behavior: 'smooth'
                });
            }
        }

        // 清空日志函数
        function clearLogs() {
            if (confirm('确定要清空日志吗？')) {
                const logsElement = document.querySelector('.logs');
                if (logsElement) {
                    logsElement.innerHTML = '';
                }
                // 同时清空localStorage中保存的日志
                localStorage.removeItem('botLogs');
            }
        }

        // 修改停止机器人函数
        function stopBot() {
            // 显示模态框而不是使用 confirm
            const stopModal = new bootstrap.Modal(document.getElementById('stopBotModal'));
            stopModal.show();
        }

        function confirmStopBot() {
            const loadingStatus = document.getElementById('botLoadingStatus');
            const loadingText = document.getElementById('botLoadingText');
            const startBtn = document.getElementById('startBotBtn');
            const stopBtn = document.getElementById('stopBotBtn');
            
            // 隐藏模态框
            const stopModal = bootstrap.Modal.getInstance(document.getElementById('stopBotModal'));
            stopModal.hide();
            
            loadingStatus.classList.remove('d-none');
            loadingText.textContent = '正在停止机器人...';
            stopBtn.disabled = true;
            
            fetch('/stop_bot')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        document.getElementById('botStatus').innerHTML = 
                            '<span class="status-stopped">已停止</span>';
                        document.getElementById('botUptime').textContent = 
                            '运行时间: 0分钟';
                        showToast('机器人已停止', 'success');
                        isPollingLogs = false;
                        startBtn.disabled = false;
                        stopBtn.disabled = true;
                    } else {
                        showToast('停止失败: ' + data.message, 'error');
                        stopBtn.disabled = false;
                    }
                    loadingStatus.classList.add('d-none');
                })
                .catch(error => {
                    showToast('停止失败: ' + error, 'error');
                    stopBtn.disabled = false;
                    loadingStatus.classList.add('d-none');
                });
        }

        // 检查更新
        function checkUpdate() {
            const logsElement = document.querySelector('.logs');

            // 添加检查更新的命令显示
            const commandLine = document.createElement('div');
            commandLine.className = 'log-line';
            commandLine.innerHTML = `<span class="command-input">$ check update</span>`;
            logsElement.appendChild(commandLine);

            fetch('/check_update')
                .then(response => response.json())
                .then(data => {
                    // 显示检查结果
                    const outputLine = document.createElement('div');
                    outputLine.className = 'log-line';
                    outputLine.innerHTML = `<span class="command-output">${data.console_output}</span>`;
                    logsElement.appendChild(outputLine);

                    // 如果有更新，显示提示信息
                    if (data.has_update) {
                        const updateInfoLine = document.createElement('div');
                        updateInfoLine.className = 'log-line';
                        updateInfoLine.innerHTML = `<span class="command-output">发现新版本: ${data.update_info.cloud_version}</span>`;
                        logsElement.appendChild(updateInfoLine);

                        const descriptionLine = document.createElement('div');
                        descriptionLine.className = 'log-line';
                        descriptionLine.innerHTML = `<span class="command-output">更新内容: ${data.update_info.description || '无详细说明'}</span>`;
                        logsElement.appendChild(descriptionLine);

                        const promptLine = document.createElement('div');
                        promptLine.className = 'log-line';
                        promptLine.innerHTML = `<span class="command-output">您可以点击"立即更新"按钮开始更新，或者在控制台输入 <span class="command-input">execute update</span> 命令开始更新</span>`;
                        logsElement.appendChild(promptLine);
                    }

                    scrollToBottom();
                })
                .catch(error => {
                    const errorLine = document.createElement('div');
                    errorLine.className = 'log-line';
                    errorLine.innerHTML = `<span class="command-error">检查更新失败: ${error}</span>`;
                    logsElement.appendChild(errorLine);
                    scrollToBottom();
                });
        }

        // 全局变量，用于跟踪更新进度
        let updateProgressInterval = null;
        let lastLogCount = 0;

        // 直接执行更新
        function executeUpdate() {
            const logsElement = document.querySelector('.logs');

            // 添加执行更新的命令显示
            const commandLine = document.createElement('div');
            commandLine.className = 'log-line';
            commandLine.innerHTML = `<span class="command-input">$ execute update</span>`;
            logsElement.appendChild(commandLine);

            // 显示开始更新的信息
            const startLine = document.createElement('div');
            startLine.className = 'log-line';
            startLine.innerHTML = `<span class="command-output">开始执行更新...</span>`;
            logsElement.appendChild(startLine);
            scrollToBottom();

            // 重置日志计数
            lastLogCount = 0;

            // 启动更新进度轮询
            if (updateProgressInterval) {
                clearInterval(updateProgressInterval);
            }

            // 开始轮询更新进度
            updateProgressInterval = setInterval(fetchUpdateProgress, 1000);

            // 发送更新请求
            fetch('/execute_update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
                .then(response => response.json())
                .then(data => {
                    // 显示最终更新结果
                    const outputLine = document.createElement('div');
                    outputLine.className = 'log-line';
                    const statusClass = data.status === 'success' ? 'command-success' : 'command-error';
                    outputLine.innerHTML = `<span class="${statusClass}">更新${data.status === 'success' ? '成功' : '失败'}: ${data.message}</span>`;
                    logsElement.appendChild(outputLine);
                    scrollToBottom();

                    // 如果更新成功且需要重启
                    if (data.status === 'success' && data.restart_required) {
                        const restartLine = document.createElement('div');
                        restartLine.className = 'log-line';
                        restartLine.innerHTML = `<span class="command-output" style="color: orange;">更新完成，需要重启应用程序以完成更新。</span>`;
                        logsElement.appendChild(restartLine);
                        scrollToBottom();
                    }

                    // 停止轮询
                    if (updateProgressInterval) {
                        clearInterval(updateProgressInterval);
                        updateProgressInterval = null;
                    }
                })
                .catch(error => {
                    console.error('执行更新失败:', error);
                    const errorLine = document.createElement('div');
                    errorLine.className = 'log-line';
                    errorLine.innerHTML = `<span class="command-error">执行更新失败: ${error.message}</span>`;
                    logsElement.appendChild(errorLine);
                    scrollToBottom();

                    // 停止轮询
                    if (updateProgressInterval) {
                        clearInterval(updateProgressInterval);
                        updateProgressInterval = null;
                    }
                });
        }

        // 获取更新进度
        function fetchUpdateProgress() {
            fetch('/update_progress')
                .then(response => response.json())
                .then(data => {
                    const logsElement = document.querySelector('.logs');

                    // 显示新的日志
                    if (data.logs && data.logs.length > lastLogCount) {
                        for (let i = lastLogCount; i < data.logs.length; i++) {
                            const log = data.logs[i];
                            const logLine = document.createElement('div');
                            logLine.className = 'log-line';
                            logLine.innerHTML = `<span class="command-output">[${log.timestamp}] ${log.message}</span>`;
                            logsElement.appendChild(logLine);
                        }
                        lastLogCount = data.logs.length;
                        scrollToBottom();
                    }

                    // 如果更新完成，停止轮询
                    if (!data.in_progress && updateProgressInterval) {
                        clearInterval(updateProgressInterval);
                        updateProgressInterval = null;
                    }
                })
                .catch(error => {
                    console.error('获取更新进度失败:', error);

                    // 出错时也停止轮询
                    if (updateProgressInterval) {
                        clearInterval(updateProgressInterval);
                        updateProgressInterval = null;
                    }
                });
        }

        // 护眼模式切换
        function toggleDarkMode() {
            document.body.setAttribute('data-bs-theme', 
                document.body.getAttribute('data-bs-theme') === 'dark' ? 'light' : 'dark');
        }

        // 页面加载初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查 localStorage 中的状态
            const darkMode = localStorage.getItem('darkMode');
            if (darkMode === 'enabled') {
                document.body.setAttribute('data-bs-theme', 'dark');
                document.getElementById('darkModeToggle').checked = true; // 更新按钮状态
            }

            // 护眼模式切换
            document.getElementById('darkModeToggle').addEventListener('change', function() {
                if (this.checked) {
                    document.body.setAttribute('data-bs-theme', 'dark');
                    localStorage.setItem('darkMode', 'enabled'); // 存储状态
                } else {
                    document.body.removeAttribute('data-bs-theme');
                    localStorage.setItem('darkMode', 'disabled'); // 存储状态
                }
            });

            // 初始化按钮状态
            const startBtn = document.getElementById('startBotBtn');
            const stopBtn = document.getElementById('stopBotBtn');
            
            // 默认启用启动按钮，禁用停止按钮
            startBtn.disabled = false;
            stopBtn.disabled = true;
            
            // 检查机器人状态
            fetch('/get_bot_logs')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success' && data.is_running) {
                        document.getElementById('botStatus').innerHTML = 
                            '<span class="status-running">运行中</span>';
                        startBtn.disabled = true;
                        stopBtn.disabled = false;
                        startPollingLogs();
                    } else {
                        document.getElementById('botStatus').innerHTML = 
                            '<span class="status-stopped">已停止</span>';
                        startBtn.disabled = false;
                        stopBtn.disabled = true;
                    }
                });
            
            // 开始定时更新
            updateSystemInfo();
            setInterval(updateSystemInfo, 2000);
            
            // 初始化用户信息（只执行一次）
            // updateUserInfo();
            
            // 初始化背景
            fetch('/get_background')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success' && data.path) {
                        document.body.style.backgroundImage = `url('${data.path}')`;
                    }
                })
                .catch(error => console.error('Error:', error));
                
            // 自动检查更新
            autoCheckUpdate();
        });
        
        // 自动检查更新函数
        function autoCheckUpdate() {
            // 检查是否在当前会话中已经检查过更新
            const hasCheckedUpdate = sessionStorage.getItem('update_checked');
            
            // 如果在当前会话中没有检查过更新，则直接运行检查更新指令
            if (!hasCheckedUpdate) {
                console.log('正在自动检查更新...');
                
                // 标记在此会话中已检查更新
                sessionStorage.setItem('update_checked', 'true');
                
                // 直接运行check update指令
                checkUpdate();
            }
        }

        // 更新用户信息
        function updateUserInfo() {
            const elements = {
                status: document.getElementById('userStatus'),
                name: document.getElementById('userName'),
                email: document.getElementById('userEmail'),
                balance: document.getElementById('userBalance'),
                chargeBalance: document.getElementById('chargeBalance'),
                totalBalance: document.getElementById('totalBalance')
            };
            
            // 设置加载状态
            Object.values(elements).forEach(el => {
                if (el) el.innerHTML = '<i class="bi bi-hourglass-split"></i> 获取中...';
            });
            
            fetch('/user_info')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // 更新状态标签
                        elements.status.className = 'badge rounded-pill ' + 
                            (data.data.status === 'normal' ? 'bg-success' : 'bg-warning');
                        elements.status.innerHTML = data.data.status === 'normal' ? 
                            '<i class="bi bi-check-circle"></i> 正常' : 
                            '<i class="bi bi-exclamation-circle"></i> ' + data.data.status;
                        
                        // 更新用户信息
                        elements.name.textContent = data.data.name;
                        elements.email.textContent = data.data.email;
                        
                        // 更新余额信息（添加货币符号和格式化）
                        elements.balance.innerHTML = `￥${parseFloat(data.data.balance).toFixed(2)}`;
                        elements.chargeBalance.innerHTML = `￥${parseFloat(data.data.charge_balance).toFixed(2)}`;
                        elements.totalBalance.innerHTML = `￥${parseFloat(data.data.total_balance).toFixed(2)}`;
                        
                        showToast('用户信息更新成功', 'success');
                    } else {
                        showToast(data.message, 'error');
                        // 显示错误状态
                        elements.status.className = 'badge rounded-pill bg-danger';
                        elements.status.innerHTML = '<i class="bi bi-x-circle"></i> 获取失败';
                        
                        Object.values(elements).forEach(el => {
                            if (el && el !== elements.status) el.textContent = '获取失败';
                        });
                    }
                })
                .catch(error => {
                    console.error('获取用户信息失败:', error);
                    showToast('网络错误', 'error');
                    
                    // 显示错误状态
                    elements.status.className = 'badge rounded-pill bg-danger';
                    elements.status.innerHTML = '<i class="bi bi-x-circle"></i> 网络错误';
                    
                    Object.values(elements).forEach(el => {
                        if (el && el !== elements.status) el.textContent = '网络错误';
                    });
                });
        }

        // 微信重连功能
        function reconnectWechat() {
            const loadingStatus = document.getElementById('botLoadingStatus');
            const loadingText = document.getElementById('botLoadingText');
            const reconnectBtn = document.getElementById('reconnectBtn');
            const originalText = reconnectBtn.innerHTML;
            
            // 显示加载状态
            loadingStatus.classList.remove('d-none');
            loadingText.textContent = '正在尝试重新连接微信...';
            reconnectBtn.disabled = true;
            reconnectBtn.innerHTML = '<i class="bi bi-arrow-repeat me-2 spin"></i>连接中...';
            
            // 向后端发送重连请求
            fetch('/reconnect_wechat')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        showToast('微信重连成功', 'success');
                        
                        // 将重连过程的日志添加到控制台
                        const logsElement = document.querySelector('.logs');
                        const logLine = document.createElement('div');
                        logLine.className = 'log-line';
                        logLine.innerHTML = `<span class="log-timestamp">[${new Date().toLocaleTimeString()}]</span><span class="log-level-success">微信重连成功</span>`;
                        logsElement.appendChild(logLine);
                        scrollToBottom();
                    } else {
                        showToast('微信重连失败: ' + data.message, 'error');
                        
                        // 将错误信息添加到控制台
                        const logsElement = document.querySelector('.logs');
                        const logLine = document.createElement('div');
                        logLine.className = 'log-line';
                        logLine.innerHTML = `<span class="log-timestamp">[${new Date().toLocaleTimeString()}]</span><span class="log-level-error">微信重连失败: ${data.message}</span>`;
                        logsElement.appendChild(logLine);
                        scrollToBottom();
                    }
                })
                .catch(error => {
                    showToast('微信重连请求失败: ' + error, 'error');
                    
                    // 将错误信息添加到控制台
                    const logsElement = document.querySelector('.logs');
                    const logLine = document.createElement('div');
                    logLine.className = 'log-line';
                    logLine.innerHTML = `<span class="log-timestamp">[${new Date().toLocaleTimeString()}]</span><span class="log-level-error">微信重连请求失败: ${error}</span>`;
                    logsElement.appendChild(logLine);
                    scrollToBottom();
                })
                .finally(() => {
                    // 恢复按钮状态和隐藏加载提示
                    reconnectBtn.disabled = false;
                    reconnectBtn.innerHTML = originalText;
                    loadingStatus.classList.add('d-none');
                });
        }

        // 背景图片处理
        document.getElementById('backgroundInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const formData = new FormData();
                formData.append('background', file);
                
                fetch('/upload_background', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        document.body.style.backgroundImage = `url('${data.path}')`;
                        showToast(data.message, 'success');
                    } else {
                        showToast(data.message, 'error');
                    }
                });
            }
        });

        // 添加命令历史记录功能
        let commandHistory = [];
        let historyIndex = -1;

        function handleConsoleInput(event) {
            const input = document.getElementById('consoleInput');
            
            // 处理方向键上下浏览历史命令
            if (event.key === 'ArrowUp') {
                event.preventDefault();
                if (historyIndex < commandHistory.length - 1) {
                    historyIndex++;
                    input.value = commandHistory[historyIndex];
                }
            } else if (event.key === 'ArrowDown') {
                event.preventDefault();
                if (historyIndex > -1) {
                    historyIndex--;
                    input.value = historyIndex >= 0 ? commandHistory[historyIndex] : '';
                }
            } else if (event.key === 'Enter') {
                event.preventDefault();
                const command = input.value.trim();
                
                if (command) {
                    // 添加到历史记录
                    commandHistory.unshift(command);
                    historyIndex = -1;
                    
                    // 显示输入的命令
                    const logsElement = document.querySelector('.logs');
                    const commandLine = document.createElement('div');
                    commandLine.className = 'log-line';
                    commandLine.innerHTML = `<span class="command-input">$ ${command}</span>`;
                    logsElement.appendChild(commandLine);
                    
                    // 执行命令
                    executeCommand(command);
                    
                    // 清空输入框
                    input.value = '';
                    
                    // 滚动到底部
                    scrollToBottom();
                }
            }
        }

        function executeCommand(command) {
            // 特殊处理 execute update 命令
            if (command.toLowerCase() === 'execute update') {
                // 先显示命令响应
                fetch('/execute_command', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ command: command })
                })
                .then(response => response.json())
                .then(data => {
                    const logsElement = document.querySelector('.logs');
                    const outputLine = document.createElement('div');
                    outputLine.className = 'log-line';

                    if (data.status === 'success') {
                        outputLine.innerHTML = `<span class="command-output">${data.output}</span>`;
                    } else {
                        outputLine.innerHTML = `<span class="command-error">${data.error}</span>`;
                    }

                    logsElement.appendChild(outputLine);
                    scrollToBottom();

                    // 如果命令成功，启动实际的更新过程
                    if (data.status === 'success') {
                        // 延迟一秒后开始更新，让用户看到响应
                        setTimeout(() => {
                            executeUpdateInternal();
                        }, 1000);
                    }
                })
                .catch(error => {
                    const logsElement = document.querySelector('.logs');
                    const errorLine = document.createElement('div');
                    errorLine.className = 'log-line';
                    errorLine.innerHTML = `<span class="command-error">执行命令失败: ${error}</span>`;
                    logsElement.appendChild(errorLine);
                    scrollToBottom();
                });
                return;
            }

            // 处理其他命令
            fetch('/execute_command', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ command: command })
            })
            .then(response => response.json())
            .then(data => {
                const logsElement = document.querySelector('.logs');

                // 处理清空命令
                if (data.clear) {
                    logsElement.innerHTML = '';
                    return;
                }

                const outputLine = document.createElement('div');
                outputLine.className = 'log-line';

                if (data.status === 'success') {
                    outputLine.innerHTML = `<span class="command-output">${data.output}</span>`;
                } else {
                    outputLine.innerHTML = `<span class="command-error">${data.error}</span>`;
                }

                logsElement.appendChild(outputLine);
                scrollToBottom();
            })
            .catch(error => {
                const logsElement = document.querySelector('.logs');
                const errorLine = document.createElement('div');
                errorLine.className = 'log-line';
                errorLine.innerHTML = `<span class="command-error">执行命令失败: ${error}</span>`;
                logsElement.appendChild(errorLine);
                scrollToBottom();
            });
        }

        // 内部更新执行函数，不显示命令行
        function executeUpdateInternal() {
            const logsElement = document.querySelector('.logs');

            // 重置日志计数
            lastLogCount = 0;

            // 启动更新进度轮询
            if (updateProgressInterval) {
                clearInterval(updateProgressInterval);
            }

            // 开始轮询更新进度
            updateProgressInterval = setInterval(fetchUpdateProgress, 1000);

            // 发送更新请求
            fetch('/execute_update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
                .then(response => response.json())
                .then(data => {
                    // 显示最终更新结果
                    const outputLine = document.createElement('div');
                    outputLine.className = 'log-line';
                    const statusClass = data.status === 'success' ? 'command-success' : 'command-error';
                    outputLine.innerHTML = `<span class="${statusClass}">更新${data.status === 'success' ? '成功' : '失败'}: ${data.message}</span>`;
                    logsElement.appendChild(outputLine);
                    scrollToBottom();

                    // 如果更新成功且需要重启
                    if (data.status === 'success' && data.restart_required) {
                        const restartLine = document.createElement('div');
                        restartLine.className = 'log-line';
                        restartLine.innerHTML = `<span class="command-output" style="color: orange;">更新完成，需要重启应用程序以完成更新。</span>`;
                        logsElement.appendChild(restartLine);
                        scrollToBottom();
                    }

                    // 停止轮询
                    if (updateProgressInterval) {
                        clearInterval(updateProgressInterval);
                        updateProgressInterval = null;
                    }
                })
                .catch(error => {
                    console.error('执行更新失败:', error);
                    const errorLine = document.createElement('div');
                    errorLine.className = 'log-line';
                    errorLine.innerHTML = `<span class="command-error">执行更新失败: ${error.message}</span>`;
                    logsElement.appendChild(errorLine);
                    scrollToBottom();

                    // 停止轮询
                    if (updateProgressInterval) {
                        clearInterval(updateProgressInterval);
                        updateProgressInterval = null;
                    }
                });
        }

        // 添加防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 添加检查状态标志
        let isCheckingDependencies = false;

        function checkAndInstallDependencies() {
            if (isCheckingDependencies) {
                return;
            }
            
            isCheckingDependencies = true;
            const statusDiv = document.getElementById('dependencyStatus');
            const statusText = document.getElementById('dependencyStatusText');
            const spinnerDiv = statusDiv.querySelector('.spinner-border');
            
            statusDiv.classList.remove('d-none', 'alert-danger', 'alert-success', 'alert-warning');
            statusDiv.classList.add('alert-info');
            statusText.textContent = '正在安装依赖，请耐心等待...';
            spinnerDiv.classList.remove('d-none');
            
            fetch('/install_dependencies', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络响应失败');
                    }
                    return response.json();
                })
                .then(data => {
                    const output = data.output || '';
                    const isSuccess = data.status === 'success' || 
                        output.toLowerCase().includes('already satisfied') || 
                        output.toLowerCase().includes('successfully installed');
                    
                    if (isSuccess) {
                        statusDiv.classList.remove('alert-info');
                        statusDiv.classList.add('alert-success');
                        spinnerDiv.classList.add('d-none');
                        statusText.innerHTML = '<i class="bi bi-check-circle-fill me-2"></i>依赖安装成功！';
                        
                        // 延迟200ms后检查依赖
                        setTimeout(checkDependencies, 200);
                        
                        // 3秒后自动隐藏成功提示
                        setTimeout(() => {
                            statusDiv.classList.add('d-none');
                        }, 3000);
                    } else {
                        throw new Error(output || '安装依赖失败');
                    }
                })
                .catch(error => {
                    statusDiv.classList.remove('alert-info');
                    statusDiv.classList.add('alert-danger');
                    spinnerDiv.classList.add('d-none');
                    statusText.innerHTML = `<i class="bi bi-x-circle-fill me-2"></i>错误: ${error.message}`;
                    
                    // 在日志区域显示错误
                    const logsElement = document.querySelector('.logs');
                    if (logsElement) {
                        const logLine = document.createElement('div');
                        logLine.className = 'log-line';
                        logLine.innerHTML = `<span class="command-error">依赖安装失败: ${error.message}</span>`;
                        logsElement.appendChild(logLine);
                        scrollToBottom();
                    }
                })
                .finally(() => {
                    isCheckingDependencies = false;
                });
        }

        // 检查依赖函数
        function checkDependencies() {
            const statusDiv = document.getElementById('dependencyStatus');
            const statusText = document.getElementById('dependencyStatusText');
            const spinnerDiv = statusDiv.querySelector('.spinner-border');
            
            statusDiv.classList.remove('d-none', 'alert-danger', 'alert-success', 'alert-warning');
            statusDiv.classList.add('alert-info');
            statusText.textContent = '正在检查依赖...';
            spinnerDiv.classList.remove('d-none');
            
            fetch('/check_dependencies')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        updateDependencyStatus(data);
                        // 修改这里：不再隐藏状态div，而是显示成功信息
                        statusDiv.classList.remove('alert-info');
                        statusDiv.classList.add('alert-success');
                        spinnerDiv.classList.add('d-none');
                        statusText.innerHTML = '<i class="bi bi-check-circle-fill me-2"></i>依赖检查完成！';
                        
                        // 3秒后自动隐藏成功提示
                        setTimeout(() => {
                            statusDiv.classList.add('d-none');
                        }, 3000);
                    } else {
                        throw new Error(data.message);
                    }
                })
                .catch(error => {
                    statusDiv.classList.remove('alert-info');
                    statusDiv.classList.add('alert-danger');
                    spinnerDiv.classList.add('d-none');
                    statusText.innerHTML = `<i class="bi bi-x-circle-fill me-2"></i>错误: ${error.message}`;
                });
        }

        // 更新依赖状态显示函数
        function updateDependencyStatus(data) {
            const depsStatusEl = document.getElementById('depsStatus');
            const missingDepsDiv = document.getElementById('missingDeps');
            const missingDepsList = missingDepsDiv.querySelector('.missing-deps-list');
            
            document.getElementById('pythonVersion').innerHTML = 
                `<i class="bi bi-filetype-py me-1"></i>${data.python_version}`;
            
            document.getElementById('pipStatus').innerHTML = data.has_pip ? 
                '<i class="bi bi-check-circle-fill text-success"></i> 已安装' : 
                '<i class="bi bi-x-circle-fill text-danger"></i> 未安装';
            document.getElementById('pipStatus').className = 
                `badge ${data.has_pip ? 'bg-success' : 'bg-danger'}`;
            
            switch(data.dependencies_status) {
                case 'complete':
                    depsStatusEl.innerHTML = '<i class="bi bi-check-circle-fill"></i> 已完成';
                    depsStatusEl.className = 'badge bg-success';
                    missingDepsDiv.classList.add('d-none');
                    break;
                case 'incomplete':
                    depsStatusEl.innerHTML = '<i class="bi bi-exclamation-circle-fill"></i> 不完整';
                    depsStatusEl.className = 'badge bg-warning';
                    missingDepsDiv.classList.remove('d-none');
                    missingDepsList.innerHTML = data.missing_dependencies.map(dep => 
                        `<span class="badge bg-warning me-2 mb-2"><i class="bi bi-box me-1"></i>${dep}</span>`
                    ).join('');
                    break;
                default:
                    depsStatusEl.innerHTML = '<i class="bi bi-question-circle-fill"></i> 未知';
                    depsStatusEl.className = 'badge bg-secondary';
                    missingDepsDiv.classList.add('d-none');
            }
        }

        // 页面加载时自动检查依赖
        document.addEventListener('DOMContentLoaded', function() {
            checkDependencies();
        });

        // 更新 GitHub 统计信息
        function updateGitHubStats() {
            const headers = {
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'KouriChat-WebUI'
            };
            
            fetch('https://api.github.com/repos/KouriChat/KouriChat', { headers })
                .then(response => response.json())
                .then(data => {
                    // 更新顶部统计信息
                    document.getElementById('githubStars').textContent = data.stargazers_count || '-';
                    document.getElementById('githubForks').textContent = data.forks_count || '-';
                    document.getElementById('githubIssues').textContent = data.open_issues_count || '-';
                    
                    // 更新页脚统计信息
                    document.getElementById('footerGithubStars').textContent = data.stargazers_count || '-';
                    document.getElementById('footerGithubForks').textContent = data.forks_count || '-';
                })
                .catch(error => {
                    console.error('获取 GitHub 统计信息失败:', error);
                    // 在获取失败时显示破折号
                    ['githubStars', 'githubForks', 'githubIssues', 
                     'footerGithubStars', 'footerGithubForks'].forEach(id => {
                        document.getElementById(id).textContent = '-';
                    });
                });
        }

        // 页面加载时获取统计信息
        document.addEventListener('DOMContentLoaded', function() {
            updateGitHubStats();
            // 每30分钟更新一次统计信息
            setInterval(updateGitHubStats, 1800000);
        });

        // 手动显示公告功能
        function showManualAnnouncement() {
            fetch('/get_announcement')
                .then(response => response.json())
                .then(data => {
                    if (data.enabled) {
                        // 设置公告内容
                        const modalTitleElement = document.getElementById('announcementModalLabel');
                        modalTitleElement.textContent = data.title;
                        modalTitleElement.classList.remove('fs-4');
                        modalTitleElement.classList.add('fw-bold', 'text-center', 'w-100', 'fs-3');

                        document.getElementById('announcementContent').innerHTML = data.content;

                        // 移除样式
                        const modalHeader = document.querySelector('#announcementModal .modal-header');
                        modalHeader.classList.remove('bg-info', 'bg-warning', 'bg-danger', 'bg-success', 'text-white', 'text-dark');
                        modalHeader.style.backgroundColor = '';

                        // 绑定"不再显示"按钮事件
                        bindAnnouncementDismissHandler(data);

                        // 显示公告模态框
                        const announcementModal = new bootstrap.Modal(document.getElementById('announcementModal'));
                        announcementModal.show();
                    } else {
                        showToast('当前没有可用的公告', 'info');
                    }
                })
                .catch(error => {
                    console.error("获取公告失败:", error);
                    showToast('获取公告失败，请稍后重试', 'error');
                });
        }

        // 更新通知功能
        function checkForUpdateNotification(forceCheck = false) {
            // 如果不是强制检查，则检查24小时限制（用于后台定时检查）
            if (!forceCheck) {
                const lastUpdateCheck = localStorage.getItem('lastBackgroundUpdateCheck');
                const now = Date.now();
                const checkInterval = 24 * 60 * 60 * 1000; // 24小时

                if (lastUpdateCheck && (now - parseInt(lastUpdateCheck)) < checkInterval) {
                    console.log('24小时内已进行过后台检查，跳过定时检查');
                    return;
                }

                // 更新后台检查时间
                localStorage.setItem('lastBackgroundUpdateCheck', now.toString());
            }

            console.log(forceCheck ? '强制检查更新（用户打开页面）' : '定时检查更新');

            fetch('/check_update')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success' && data.has_update) {
                        // 检查是否已经忽略了这个版本
                        const dismissedVersion = localStorage.getItem('dismissedUpdateVersion');
                        const latestVersion = data.update_info?.cloud_version;

                        if (dismissedVersion === latestVersion) {
                            console.log('用户已忽略此版本更新:', latestVersion);
                            return;
                        }

                        showUpdateNotification(data.update_info);
                    } else {
                        console.log('当前已是最新版本');
                    }
                })
                .catch(error => {
                    console.error('检查更新失败:', error);
                });
        }

        function showUpdateNotification(updateInfo) {
            // 设置更新信息
            document.getElementById('currentVersionText').textContent = updateInfo.local_version || '-';
            document.getElementById('latestVersionText').textContent = updateInfo.cloud_version || '-';
            document.getElementById('updateDescription').textContent = updateInfo.description || '暂无更新说明';
            document.getElementById('updateTime').innerHTML =
                `<i class="bi bi-calendar me-1"></i>${updateInfo.last_update || '未知'}`;

            // 绑定按钮事件
            const updateNowBtn = document.getElementById('updateNowBtn');
            const updateLaterBtn = document.getElementById('updateLaterBtn');

            // 移除之前的事件监听器
            const newUpdateNowBtn = updateNowBtn.cloneNode(true);
            const newUpdateLaterBtn = updateLaterBtn.cloneNode(true);
            updateNowBtn.parentNode.replaceChild(newUpdateNowBtn, updateNowBtn);
            updateLaterBtn.parentNode.replaceChild(newUpdateLaterBtn, updateLaterBtn);

            // 立即更新按钮
            newUpdateNowBtn.addEventListener('click', function() {
                // 关闭更新通知模态框
                const updateModal = bootstrap.Modal.getInstance(document.getElementById('updateNotificationModal'));
                if (updateModal) {
                    updateModal.hide();
                }

                // 直接执行更新，不需要控制台确认
                setTimeout(() => {
                    executeUpdate();
                }, 500);
            });

            // 稍后更新按钮
            newUpdateLaterBtn.addEventListener('click', function() {
                // 记录用户选择稍后更新，24小时内后台不再提醒
                // 但用户主动打开页面时仍会检查
                localStorage.setItem('lastBackgroundUpdateCheck', Date.now().toString());

                // 可选：记录用户忽略的版本，避免同一版本重复提醒
                const latestVersion = document.getElementById('latestVersionText').textContent;
                if (latestVersion && latestVersion !== '-') {
                    localStorage.setItem('dismissedUpdateVersion', latestVersion);
                }
            });

            // 显示更新通知模态框
            const updateNotificationModal = new bootstrap.Modal(document.getElementById('updateNotificationModal'));
            updateNotificationModal.show();
        }

        // 公告功能
        if (shouldShowAnnouncement) {
            document.addEventListener('DOMContentLoaded', function() {
                // 直接获取公告并显示
                fetch('/get_announcement')
                    .then(response => response.json())
                    .then(data => {
                        if (data.enabled) {
                            // 设置公告内容
                            const modalTitleElement = document.getElementById('announcementModalLabel');
                            modalTitleElement.textContent = data.title;
                            modalTitleElement.classList.remove('fs-4'); // 明确移除之前的 fs-4
                            modalTitleElement.classList.add('fw-bold', 'text-center', 'w-100', 'fs-3'); // 加粗、居中、占满宽度，并使用 fs-3 字体大小

                            document.getElementById('announcementContent').innerHTML = data.content;

                            // 移除根据公告类型设置样式的代码
                            const modalHeader = document.querySelector('#announcementModal .modal-header');
                            modalHeader.classList.remove('bg-info', 'bg-warning', 'bg-danger', 'bg-success', 'text-white', 'text-dark');
                            modalHeader.style.backgroundColor = '';

                            // 绑定"不再显示"按钮事件
                            bindAnnouncementDismissHandler(data);

                            // 强制显示公告模态框
                            const announcementModal = new bootstrap.Modal(document.getElementById('announcementModal'));
                            announcementModal.show();
                        }
                    })
                    .catch(error => console.error("获取公告失败:", error));

                // 处理"我知道了"按钮点击事件
                document.getElementById('announcementOkBtn').addEventListener('click', function() {
                    // 无需特殊处理
                });
            });
        }

        // 页面加载时恢复日志
        document.addEventListener('DOMContentLoaded', function() {
            // 从localStorage中恢复日志
            const savedLogs = JSON.parse(localStorage.getItem('botLogs') || '[]');
            if (savedLogs.length > 0) {
                const logsElement = document.querySelector('.logs');

                savedLogs.forEach(log => {
                    const logLine = document.createElement('div');
                    logLine.className = 'log-line';
                    logLine.innerHTML = formatLogLine(log);
                    logsElement.appendChild(logLine);
                });

                // 滚动到底部
                scrollToBottom();
            }

            // 获取按钮引用
            const startBtn = document.getElementById('startBotBtn');
            const stopBtn = document.getElementById('stopBotBtn');

            // 检查机器人状态
            fetch('/get_bot_logs')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success' && data.is_running) {
                        document.getElementById('botStatus').innerHTML =
                            '<span class="status-running">运行中</span>';
                        startBtn.disabled = true;
                        stopBtn.disabled = false;
                        startPollingLogs();
                    } else {
                        document.getElementById('botStatus').innerHTML =
                            '<span class="status-stopped">已停止</span>';
                        startBtn.disabled = false;
                        stopBtn.disabled = true;
                    }
                });

            // 延迟3秒后检查更新通知（避免与公告冲突）
            // 用户打开页面时强制检查更新，不受24小时限制
            setTimeout(() => {
                checkForUpdateNotification(true); // true表示强制检查
            }, 3000);

            // 设置定时检查：每24小时检查一次更新（后台检查，受24小时限制）
            setInterval(() => {
                checkForUpdateNotification(false); // false表示后台定时检查
            }, 24 * 60 * 60 * 1000); // 24小时
        });
    </script>
</body>
</html> 