<!-- 监听用户列表配置 -->
<div class="mb-2">
    <div class="input-group mb-2">
        <input type="text" class="form-control"
            id="input_{{ key }}"
            placeholder="请输入要监听的用户">
        <button class="btn btn-primary" type="button"
            onclick="addNewUser('{{ key }}')"
            title="添加用户">
            添加 <i class="bi bi-plus-lg"></i>
        </button>
    </div>
    <div id="selected_users_{{ key }}" class="list-group">
        {% if config.value %}
            {% for user in config.value %}
                {% if user %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        {{ user }}
                        <button type="button" class="btn btn-danger btn-sm" onclick="removeUser('{{ key }}', '{{ user }}')" title="删除用户">
                            <i class="bi bi-x-lg"></i>
                        </button>
                    </div>
                {% endif %}
            {% endfor %}
        {% endif %}
    </div>
</div>
<input type="text" class="form-control"
    id="{{ key }}" name="{{ key }}"
    value="{{ config.value|join(',') }}"
    placeholder="多个值用英文逗号分隔"
    readonly
    style="display: none;">