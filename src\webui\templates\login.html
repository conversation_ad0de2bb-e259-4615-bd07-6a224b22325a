{% extends "auth_base.html" %}

{% block title %}登录{% endblock %}

{% block header %}KouriChat{% endblock %}
{% block subheader %}请输入管理密码以继续{% endblock %}

{% block content %}
<form id="loginForm" onsubmit="handleLogin(event)">
    <div class="password-input-group mb-3">
        <input type="password" 
               class="form-control" 
               id="password" 
               placeholder="请输入管理密码"
               required>
        <button type="button" 
                class="password-toggle" 
                onclick="togglePassword('password')"
                aria-label="切换密码显示">
            <i class="bi bi-eye"></i>
        </button>
    </div>

    <div class="form-check mb-3">
        <input class="form-check-input" type="checkbox" id="rememberMe">
        <label class="form-check-label" for="rememberMe">
            记住我的登录状态
        </label>
    </div>

    <button type="submit" class="btn btn-primary w-100">
        <i class="bi bi-box-arrow-in-right me-2"></i>登录
    </button>
</form>
{% endblock %}

{% block extra_script %}
<script>
    function handleLogin(event) {
        event.preventDefault();
        
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('rememberMe').checked;
        
        fetch('/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                password: password,
                remember_me: rememberMe
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                window.location.href = '/dashboard';
            } else {
                showError(data.message || '登录失败');
            }
        })
        .catch(error => {
            showError('网络错误，请重试');
        });
    }
</script>
{% endblock %} 