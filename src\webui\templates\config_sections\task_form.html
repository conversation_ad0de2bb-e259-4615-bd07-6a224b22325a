<!-- 任务表单 -->
<form id="taskForm">
    <div class="row">
        <!-- 左侧：基本信息 -->
        <div class="col-md-6">
            <div class="mb-3">
                <label class="form-label">
                    <i class="bi bi-key me-2"></i>任务ID
                </label>
                <input type="text" class="form-control" id="taskId" required>
            </div>
            <div class="mb-3">
                <label class="form-label">
                    <i class="bi bi-person me-2"></i>发送对象
                </label>
                <select class="form-select" id="taskChatId" required>
                    <option value="">请选择发送对象</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">
                    <i class="bi bi-chat-text me-2"></i>消息内容
                </label>
                <textarea class="form-control" id="taskContent" rows="3" required></textarea>
            </div>
        </div>

        <!-- 右侧：定时设置 -->
        <div class="col-md-6">
            <div class="mb-3">
                <label class="form-label">
                    <i class="bi bi-alarm me-2"></i>定时类型
                </label>
                <select class="form-select" id="scheduleType" onchange="toggleScheduleInput()">
                    <option value="cron">Cron表达式</option>
                    <option value="interval">时间间隔</option>
                </select>
            </div>

            <!-- Cron表达式设置 -->
            <div id="cronInputGroup" class="mb-3">
                <label class="form-label">
                    <i class="bi bi-calendar3 me-2"></i>执行时间
                </label>
                <div class="row g-2">
                    <div class="col-6">
                        <select class="form-select" id="cronHour">
                            <option value="*">每小时</option>
                            {% for hour in range(24) %}
                            <option value="{{ hour }}">{{ hour }}点</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-6">
                        <select class="form-select" id="cronMinute">
                            <option value="0">整点</option>
                            <option value="30">30分</option>
                            <option value="15">15分</option>
                            <option value="45">45分</option>
                        </select>
                    </div>
                </div>
                <div class="mt-2">
                    <label class="form-label">
                        <i class="bi bi-calendar-week me-2"></i>执行周期
                    </label>
                    <div class="btn-group w-100 flex-wrap" role="group">
                        {% set weekdays = ['一', '二', '三', '四', '五', '六', '日'] %}
                        {% for i in range(1, 8) %}
                        <input type="checkbox" class="btn-check" id="cronWeekday{{ i }}" autocomplete="off">
                        <label class="btn btn-outline-primary flex-fill" for="cronWeekday{{ i }}">{{ weekdays[i-1] }}</label>
                        {% endfor %}
                    </div>
                </div>
                <!-- 隐藏的cron表达式输入框 -->
                <input type="hidden" id="cronExpression" value="">
            </div>

            <!-- 时间间隔设置 -->
            <div id="intervalInputGroup" class="mb-3" style="display: none;">
                <label class="form-label">
                    <i class="bi bi-hourglass-split me-2"></i>间隔时间
                </label>
                <div class="input-group">
                    <input type="number" class="form-control" id="intervalValue"
                           min="1" step="1" placeholder="输入数值">
                    <select class="form-select" id="intervalUnit" style="max-width: 120px;">
                        <option value="60">分钟</option>
                        <option value="3600">小时</option>
                        <option value="86400">天</option>
                    </select>
                </div>
                <div class="form-text">
                    <i class="bi bi-info-circle me-1"></i>
                    常用间隔：
                    <div class="btn-group mt-1">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setInterval(30, '60')">30分钟</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setInterval(1, '3600')">1小时</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setInterval(2, '3600')">2小时</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setInterval(24, '3600')">1天</button>
                    </div>
                </div>
            </div>

            <!-- 预览 -->
            <div class="mt-3">
                <label class="form-label">
                    <i class="bi bi-eye me-2"></i>执行时间预览
                </label>
                <div id="schedulePreview" class="form-control">
                    请选择定时类型和设置
                </div>
            </div>
        </div>
    </div>
</form>