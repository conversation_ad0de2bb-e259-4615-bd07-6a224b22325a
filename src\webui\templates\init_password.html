{% extends "auth_base.html" %}

{% block title %}初始化密码{% endblock %}

{% block header %}初始化管理密码{% endblock %}
{% block subheader %}请设置管理员密码以继续使用{% endblock %}

{% block content %}
<form id="initForm" onsubmit="handleInit(event)">
    <div class="password-input-group mb-3">
        <input type="password" 
               class="form-control" 
               id="password" 
               placeholder="请输入管理密码"
               required>
        <button type="button" 
                class="password-toggle" 
                onclick="togglePassword('password')">
            <i class="bi bi-eye"></i>
        </button>
    </div>

    <div class="password-input-group mb-3">
        <input type="password" 
               class="form-control" 
               id="confirmPassword" 
               placeholder="请确认管理密码"
               required>
        <button type="button" 
                class="password-toggle" 
                onclick="togglePassword('confirmPassword')">
            <i class="bi bi-eye"></i>
        </button>
    </div>

    <button type="submit" class="btn btn-primary w-100">
        <i class="bi bi-check-lg me-2"></i>设置密码
    </button>
</form>
{% endblock %}

{% block extra_script %}
<script>
    function handleInit(event) {
        event.preventDefault();
        
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        
        if (password !== confirmPassword) {
            showError('两次输入的密码不一致');
            return;
        }
        
        fetch('/init_password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                password: password,
                remember_me: true
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // 跳转到快速设置页面
                window.location.href = '/quick_setup';
            } else {
                showError(data.message || '密码设置失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('网络错误，请重试');
        });
    }
</script>
{% endblock %} 