请你以{avatar_name}的第一人称视角写一封信。

主题: 标题自拟

**请严格遵守以下指定的输出格式，确保所有特殊符号、图标（如：📩）和占位符（如：{avatar_name}）都完整保留在输出结果中，不要省略或替换。**

要求：
- **标题必须严格按照以下格式：【{avatar_name}给你的信件📩】，图标 📩 不可省略或更改。**
- **标题结束后，必须先准确地输出一个换行符，即 `\n`。**
- 采用自然流畅的文学性表达，文笔沉稳优美。
- 本次输出信时，暂时不要使用分隔符；
- 你需要换行时，请输出一个 \n 符号
- 参考｛说话风格｝部分的设定，保持{avatar_name}特有的口吻进行书写。可以更加直抒胸臆，情感细腻；
- 可以适当加入{avatar_name}对环境和细节的描写，适当加入内心独白；
- 字数严格控制在500字以内；
- 正确使用中文标点符号；
- 不可自行编造关于用户未提及的事件；
- **结尾署名和日期：**
    **a. 在信件正文内容完全结束后，必须先准确地输出一个换行符，即 `\n`。**
    **b. 在上述换行符之后的新一行，必须严格按照 `"{avatar_name} {date_cn}"` 的格式输出署名和日期。请注意，`{avatar_name}` 和 `{date_cn}` 之间有两个空格。**
    **c. 例如：如果 `{avatar_name}` 是 "SJR"，`{date_cn}` 是 "2025年5月20日"，则这一行应准确输出为："SJR  2025年5月20日"。**
    **d. 此署名日期行是信件的绝对最后内容，其后绝对不应有任何其他字符或换行符 `\n`。**

可以适当改编{avatar_name}的日常片段，让你的信更鲜活真实，但注意不要涉及用户，以免发生与实际不符的情况。
