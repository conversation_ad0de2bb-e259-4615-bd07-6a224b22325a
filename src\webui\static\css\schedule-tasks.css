/* 定时任务样式 */
.task-list-item {
    transition: all 0.3s ease;
    border-radius: 0.5rem;
    margin-bottom: 0.75rem;
}

.task-list-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.task-badge {
    font-size: 0.8rem;
    padding: 0.35em 0.65em;
}

.task-controls {
    display: flex;
    gap: 0.5rem;
}

.task-controls .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.schedule-preview {
    background-color: rgba(var(--bs-primary-rgb), 0.05);
    border: 1px solid rgba(var(--bs-primary-rgb), 0.1);
    border-radius: 0.375rem;
    padding: 0.75rem;
    font-size: 0.9rem;
}

/* 日期选择按钮组样式 */
.weekday-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-top: 0.5rem;
}

.weekday-selector .btn {
    flex: 1 1 calc(14.28% - 0.25rem);
    min-width: 40px;
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
    text-align: center;
}

/* 在小屏幕上调整按钮大小 */
@media (max-width: 768px) {
    .weekday-selector .btn {
        flex: 1 1 calc(33.33% - 0.25rem);
        min-width: 30px;
        padding: 0.25rem 0.375rem;
        font-size: 0.75rem;
    }
}

/* 在更小的屏幕上进一步调整 */
@media (max-width: 480px) {
    .weekday-selector .btn {
        flex: 1 1 calc(50% - 0.25rem);
        min-width: 25px;
        padding: 0.25rem;
        font-size: 0.75rem;
    }
}

/* 任务状态徽章 */
.task-status-badge {
    position: relative;
    padding-left: 1.5rem;
}

.task-status-badge::before {
    content: '';
    position: absolute;
    left: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
}

.task-status-active::before {
    background-color: var(--bs-success);
}

.task-status-inactive::before {
    background-color: var(--bs-secondary);
}